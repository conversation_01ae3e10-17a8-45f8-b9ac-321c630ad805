﻿using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using aibase.Downholes.Dto;
using aibase.Downholes.Services.AssayService;
using aibase.Downholes.Services.DownholeSurveyService;
using aibase.Downholes.Services.GeologyService;
using aibase.Downholes.Services.GeophysicsService;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoles.Dto;

namespace aibase.Downholes;

/// <summary>
/// 
/// </summary>
public class DownholeAppService : aibaseAppServiceBase, IDownholeAppService
{
    private readonly IAssayService _assayService;
    private readonly IGeologyService _geologyService;
    private readonly IGeophysicsService _geophysicsService;
    private readonly IDownholeSurveyService _downholeSurveyService;

    /// <inheritdoc />
    public DownholeAppService(
        IAssayService assayService,
        IGeophysicsService geophysicsService, 
        IGeologyService geologyService, IDownholeSurveyService downholeSurveyService)
    {
        _assayService = assayService;
        _geologyService = geologyService;
        _downholeSurveyService = downholeSurveyService;
        _geophysicsService = geophysicsService;
    }


    /// <inheritdoc />
    public async Task<IActionResult> UploadDownholeData([FromForm] UploadDownholeDataDto input)
    {
        return await _geophysicsService.UploadGeophysicsDataAsync(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> UploadAssayData([FromForm] UploadAssayDataDto input)
    {
        return await _assayService.UploadAssayData(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> GetAssayData(PagedAssayResultRequestDto input)
    {
        return await _assayService.GetAssaysDataByProject(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> GetAllAssayDataByDrillholeAsync(PagedAssayDataByDrillholeRequestDto input)
    {
        return await _assayService.GetAllAssayDataByDrillholeAsync(input);
    }

    /// <inheritdoc />
    public async Task CreateGeologyDataPointAsync(CreateGeologyDataDto input)
    {
        await _geologyService.CreateGeologyDataPoint(input);
    }

    /// <inheritdoc />
    public async Task UpdateGeologyDataPointAsync(UpdateGeologyDataPointDto input)
    {
        await _geologyService.UpdateGeologyDataPoint(input);
    }
        
    /// <inheritdoc />
    public async Task DeleteGeologyDataPointAsync(string groupId)
    {
        await _geologyService.DeleteGeologyDataPoint(groupId);
    }
        
    /// <inheritdoc />
    public async Task<IActionResult> GetGeologyData(PagedGeologyResultRequestDto input)
    {
        return await _geologyService.GetGeologyDataByProject(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> UploadGeologyDataAsync([FromForm] UploadGeologyDataDto input)
    {
        return await _geologyService.UploadGeologyData(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> GetDownholeByProject(PagedDownholeResultRequestDto input)
    {
        return await _geophysicsService.GetGeophysicsDataByProjectAsync(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> ValidateDownholeData([FromForm] UploadDownholeDataDto input)
    {
        return await _geophysicsService.ValidateGeophysicsDataAsync(input);
    }

    /// <inheritdoc />
    public async Task<IActionResult> ValidateGeologyData([FromForm] ValidateFileUploadGeology input)
    {
        return await _geologyService.ValidateGeologyData(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DownholeSurveyDto>> GetDownholeSurveyDataAsync(PagedDownholeSurveyResultRequestDto input)
    {
        return await _downholeSurveyService.GetDownholeSurveyDataAsync(input);
    }
        
    /// <inheritdoc />
    public async Task CalculateDesurveyAsync(CalculateDesurveyDto input)
    {
        await _downholeSurveyService.CalculateDesurveyAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DesurveyResultDto>> GetAllDesurveyResultAsync(PagedDesurveyResultRequestDto input)
    {
        return await _downholeSurveyService.GetAllDesurveyResultAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillHoleDto>> GetAllDesurveyResultByDrillHoleAsync(GetAllDesurveyResultByDrillHoleDto input)
    {
        return await _downholeSurveyService.GetAllDesurveyResultByDrillHoleAsync(input);
    }
}