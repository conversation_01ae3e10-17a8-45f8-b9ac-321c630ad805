using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using System.Transactions;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DesurveyResults;
using aibase.DownholeSurveys;
using aibase.Downholes.Dto;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using aibase.EntityFrameworkCore;
using aibase.ImportDataServices.Dto;
using AutoMapper;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;

namespace aibase.Downholes.Services.DownholeSurveyService;

/// <inheritdoc />
public class DownholeSurveyService : IDownholeSurveyService
{
    private readonly IDbContextProvider<aibaseDbContext> _dbContextProvider;
    private readonly IRepository<DownholeSurvey, int> _repository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<DownholeSurveyType, int> _downholeSurveyTypeRepository;
    private readonly IRepository<DesurveyResult, int> _desurveyResultRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public DownholeSurveyService(
        IDbContextProvider<aibaseDbContext> dbContextProvider,
        IRepository<DownholeSurvey, int> repository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<DownholeSurveyType, int> downholeSurveyTypeRepository,
        IRepository<DesurveyResult, int> desurveyResultRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IMapper mapper,
        IAbpSession abpSession
        )
    {
        _dbContextProvider = dbContextProvider;
        _repository = repository;
        _drillHoleRepository = drillHoleRepository;
        _downholeSurveyTypeRepository = downholeSurveyTypeRepository;
        _desurveyResultRepository = desurveyResultRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _mapper = mapper;
        _abpSession = abpSession;
    }


    /// <inheritdoc />
    public async Task<PagedResultDto<DownholeSurveyDto>> GetDownholeSurveyDataAsync(
        PagedDownholeSurveyResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.ProjectId.HasValue, x => x.ProjectId == input.ProjectId)
            .WhereIf(input.ProspectId.HasValue, x => x.ProspectId == input.ProspectId)
            .WhereIf(input.DrillHoleId.HasValue, x => x.DrillHoleId == input.DrillHoleId)
            .WhereIf(input.DepthFrom.HasValue, x => input.DepthFrom != null && x.Depth >= input.DepthFrom.Value)
            .WhereIf(input.DepthTo.HasValue, x => input.DepthTo != null && x.Depth <= input.DepthTo.Value)
            .OrderBy(x => x.Depth);

        var totalCount = await query.CountAsync();

        var results = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<DownholeSurveyDto>(totalCount, _mapper.Map<List<DownholeSurveyDto>>(results));
    }

    /// <inheritdoc />
    public async Task<int> UploadDownholeSurveyDataAsync(UploadDownholeSurveyDataDto input,
        bool isDelete = true)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        var dataToInsert = new List<DownholeSurvey>();
        List<int> drillHoleIds = [];

        var drillHoles = await _drillHoleRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId)
            .ToListAsync();

        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        for (var row = 2; row <= rowCount; row++)
                        {
                            var drillHoleNameFile = worksheet.Cells[row, 1].Text;
                            var depth = double.TryParse(worksheet.Cells[row, 2].Text, out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(worksheet.Cells[row, 3].Text, out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(worksheet.Cells[row, 4].Text, out var azimuthVal)
                                ? azimuthVal
                                : 0;
                            var type = worksheet.Cells[row, 5].Text;

                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            drillHoleIds.Add(drillHole.Id);
                            dataToInsert.Add(new DownholeSurvey
                            {
                                ProjectId = input.ProjectId,
                                ProspectId = input.ProspectId,
                                DrillHoleId = drillHole.Id,
                                Depth = depth,
                                Dip = dip,
                                Azimuth = azimuth,
                                Type = type,
                                CreationTime = DateTime.Now,
                                TenantId = tenantId
                            });
                        }
                    }

                    break;
                }
                case ".csv":
                {
                    using (var reader = new StreamReader(stream))
                    {
                        await reader.ReadLineAsync(); // Skip header

                        while (await reader.ReadLineAsync() is { } line)
                        {
                            var values = line.Split(',');
                            if (values.Length < 5) continue;

                            var drillHoleNameFile = values[0].Trim();
                            var depth = double.TryParse(values[1].Trim(), out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(values[2].Trim(), out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(values[3].Trim(), out var azimuthVal) ? azimuthVal : 0;
                            var type = values[4].Trim();

                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            drillHoleIds.Add(drillHole.Id);
                            dataToInsert.Add(new DownholeSurvey
                            {
                                ProjectId = input.ProjectId,
                                ProspectId = input.ProspectId,
                                DrillHoleId = drillHole.Id,
                                Depth = depth,
                                Dip = dip,
                                Azimuth = azimuth,
                                Type = type,
                                CreationTime = DateTime.Now,
                                TenantId = tenantId
                            });
                        }
                    }

                    break;
                }
                default:
                    throw new UserFriendlyException("Unsupported file format. Please upload a .xlsx or .csv file.");
            }
        }

        if (isDelete)
        {
            await DeleteDownholeSurveyDataAsync(input.ProjectId, drillHoleIds.Distinct().ToList());
        }

        await InsertDownholeSurveyDataAsync(dataToInsert);

        // await _unitOfWorkManager.Current.SaveChangesAsync();
        return dataToInsert.Count;
    }

    /// <inheritdoc />
    public async Task<int> UpdateDownholeSurveyDataAsync(UploadDownholeSurveyDataDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        var dataToUpdate = new List<DownholeSurvey>();

        var drillHoles = await _drillHoleRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId)
            .ToListAsync();

        // Get existing records to update
        var existingRecords = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId && x.ProspectId == input.ProspectId)
            .ToListAsync();

        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        for (var row = 2; row <= rowCount; row++)
                        {
                            var drillHoleNameFile = worksheet.Cells[row, 1].Text;
                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            var depth = double.TryParse(worksheet.Cells[row, 2].Text, out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(worksheet.Cells[row, 3].Text, out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(worksheet.Cells[row, 4].Text, out var azimuthVal)
                                ? azimuthVal
                                : 0;
                            var type = worksheet.Cells[row, 5].Text;

                            var existingRecord = existingRecords.FirstOrDefault(x =>
                                x.DrillHoleId == drillHole.Id && Math.Abs(x.Depth - depth) < 0.001);

                            if (existingRecord != null)
                            {
                                existingRecord.Dip = dip;
                                existingRecord.Azimuth = azimuth;
                                existingRecord.Type = type;
                                existingRecord.LastModificationTime = DateTime.Now;
                                dataToUpdate.Add(existingRecord);
                            }
                        }
                    }

                    break;
                }
                case ".csv":
                {
                    using (var reader = new StreamReader(stream))
                    {
                        await reader.ReadLineAsync(); // Skip header

                        while (await reader.ReadLineAsync() is { } line)
                        {
                            var values = line.Split(',');
                            if (values.Length < 5) continue;

                            var drillHoleNameFile = values[0].Trim();
                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            var depth = double.TryParse(values[1].Trim(), out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(values[2].Trim(), out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(values[3].Trim(), out var azimuthVal) ? azimuthVal : 0;
                            var type = values[4].Trim();

                            var existingRecord = existingRecords.FirstOrDefault(x =>
                                x.DrillHoleId == drillHole.Id && Math.Abs(x.Depth - depth) < 0.001);

                            if (existingRecord != null)
                            {
                                existingRecord.Dip = dip;
                                existingRecord.Azimuth = azimuth;
                                existingRecord.Type = type;
                                existingRecord.LastModificationTime = DateTime.Now;
                                dataToUpdate.Add(existingRecord);
                            }
                        }
                    }

                    break;
                }
                default:
                    throw new UserFriendlyException("Unsupported file format. Please upload a .xlsx or .csv file.");
            }
        }

        if (dataToUpdate.Count != 0)
        {
            await UpdateDownholeSurveyDataAsync(dataToUpdate);
        }

        // await _unitOfWorkManager.Current.SaveChangesAsync();
        return dataToUpdate.Count;
    }

    /// <inheritdoc />
    public async Task<int> AddAndUpdateDownholeSurveyDataAsync(UploadDownholeSurveyDataDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        var dataToUpdate = new List<DownholeSurvey>();
        var dataToInsert = new List<DownholeSurvey>();

        var drillHoles = await _drillHoleRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId)
            .ToListAsync();

        // Get existing records
        var existingRecords = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId && x.ProspectId == input.ProspectId)
            .ToListAsync();

        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using (var package = new ExcelPackage(stream))
                    {
                        var worksheet = package.Workbook.Worksheets[0];
                        var rowCount = worksheet.Dimension.End.Row;

                        for (var row = 2; row <= rowCount; row++)
                        {
                            var drillHoleNameFile = worksheet.Cells[row, 1].Text;
                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            var depth = double.TryParse(worksheet.Cells[row, 2].Text, out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(worksheet.Cells[row, 3].Text, out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(worksheet.Cells[row, 4].Text, out var azimuthVal)
                                ? azimuthVal
                                : 0;
                            var type = worksheet.Cells[row, 5].Text;

                            var existingRecord = existingRecords.FirstOrDefault(x =>
                                x.DrillHoleId == drillHole.Id && Math.Abs(x.Depth - depth) < 0.001);

                            if (existingRecord != null)
                            {
                                // Update existing record
                                existingRecord.Dip = dip;
                                existingRecord.Azimuth = azimuth;
                                existingRecord.Type = type;
                                existingRecord.LastModificationTime = DateTime.Now;
                                dataToUpdate.Add(existingRecord);
                            }
                            else
                            {
                                // Add new record
                                dataToInsert.Add(new DownholeSurvey
                                {
                                    ProjectId = input.ProjectId,
                                    ProspectId = input.ProspectId,
                                    DrillHoleId = drillHole.Id,
                                    Depth = depth,
                                    Dip = dip,
                                    Azimuth = azimuth,
                                    Type = type,
                                    CreationTime = DateTime.Now,
                                    TenantId = tenantId
                                });
                            }
                        }
                    }

                    break;
                }
                case ".csv":
                {
                    using (var reader = new StreamReader(stream))
                    {
                        await reader.ReadLineAsync(); // Skip header

                        while (await reader.ReadLineAsync() is { } line)
                        {
                            var values = line.Split(',');
                            if (values.Length < 5) continue;

                            var drillHoleNameFile = values[0].Trim();
                            var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                            if (drillHole == null) continue;

                            var depth = double.TryParse(values[1].Trim(), out var depthVal) ? depthVal : 0;
                            var dip = double.TryParse(values[2].Trim(), out var dipVal) ? dipVal : 0;
                            var azimuth = double.TryParse(values[3].Trim(), out var azimuthVal) ? azimuthVal : 0;
                            var type = values[4].Trim();

                            var existingRecord = existingRecords.FirstOrDefault(x =>
                                x.DrillHoleId == drillHole.Id && Math.Abs(x.Depth - depth) < 0.001);

                            if (existingRecord != null)
                            {
                                // Update existing record
                                existingRecord.Dip = dip;
                                existingRecord.Azimuth = azimuth;
                                existingRecord.Type = type;
                                existingRecord.LastModificationTime = DateTime.Now;
                                dataToUpdate.Add(existingRecord);
                            }
                            else
                            {
                                // Add new record
                                dataToInsert.Add(new DownholeSurvey
                                {
                                    ProjectId = input.ProjectId,
                                    ProspectId = input.ProspectId,
                                    DrillHoleId = drillHole.Id,
                                    Depth = depth,
                                    Dip = dip,
                                    Azimuth = azimuth,
                                    Type = type,
                                    CreationTime = DateTime.Now,
                                    TenantId = tenantId
                                });
                            }
                        }
                    }

                    break;
                }
                default:
                    throw new UserFriendlyException("Unsupported file format. Please upload a .xlsx or .csv file.");
            }
        }

        await AddAndUpdateDownholeSurveyDataAsync(dataToUpdate, dataToInsert);

        // await _unitOfWorkManager.Current.SaveChangesAsync();
        return dataToUpdate.Count + dataToInsert.Count;
    }

    /// <inheritdoc />
    public async Task<List<string>> ValidationDownholeSurveyDataAsync(UploadDownholeSurveyDataDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var drillHoles = input.DrillHoles;
        var mappingFields = input.ImportMappingTemplateFields;
        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();
        var errors = new List<string>();

        var downholeSurveyTypeNames = await _downholeSurveyTypeRepository.GetAll()
            .Where(x => x.TenantId == _abpSession.GetTenantId())
            .Select(x => x.Name)
            .Distinct()
            .ToListAsync();

        // Dictionary to store existing depths for each drillhole and type combination
        var existingDepths = new Dictionary<(int DrillHoleId, string Type), List<double>>();

        // Get existing depths from database for all drillholes with their types
        foreach (var drillHole in drillHoles)
        {
            var depthsByType = await _repository.GetAll()
                .Where(x => x.DrillHoleId == drillHole.Id)
                .Select(x => new { x.Depth, x.Type })
                .ToListAsync();

            foreach (var item in depthsByType.GroupBy(x => x.Type))
            {
                existingDepths[(drillHole.Id, item.Key)] = item.Select(x => x.Depth).ToList();
            }
        }

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            var drillHoleField = mappingFields.FirstOrDefault(f => f.SystemFieldName == "Drill Hole");
            var depthField = mappingFields.FirstOrDefault(f => f.SystemFieldName == "Depth");
            var typeField = mappingFields.FirstOrDefault(f => f.SystemFieldName == "Type");

            if (drillHoleField == null || depthField == null || typeField == null)
            {
                errors.Add("Required mapping fields 'Drill Hole', 'Depth', 'Type' not found.");
                return errors;
            }

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using var package = new ExcelPackage(stream);
                    var worksheet = package.Workbook.Worksheets[0];
                    var rowCount = worksheet.Dimension.End.Row;
                    var colCount = worksheet.Dimension.End.Column;

                    // Get column indices from headers
                    const int headerRow = 1;
                    var drillHoleColIndex = -1;
                    var depthColIndex = -1;
                    var typeColIndex = -1;

                    for (var col = 1; col <= colCount; col++)
                    {
                        var headerText = worksheet.Cells[headerRow, col].Text.Trim();
                        if (headerText == drillHoleField.FileColumnName)
                            drillHoleColIndex = col;
                        else if (headerText == depthField.FileColumnName)
                            depthColIndex = col;
                        else if (headerText == typeField.FileColumnName)
                            typeColIndex = col;
                    }

                    if (drillHoleColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{drillHoleField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    if (depthColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{depthField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    if (typeColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{typeField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    for (var row = 2; row <= rowCount; row++)
                    {
                        var drillHoleNameFile = worksheet.Cells[row, drillHoleColIndex].Text.Trim();
                        var depthText = worksheet.Cells[row, depthColIndex].Text.Trim();
                        var typeText = worksheet.Cells[row, typeColIndex].Text.Trim();

                        if (!downholeSurveyTypeNames.Contains(typeText))
                        {
                            errors.Add($"Downhole Survey Type {typeText} not found.");
                            continue;
                        }

                        if (!double.TryParse(depthText, out var depthValue) || depthValue < 0)
                        {
                            errors.Add($"Depth value at row {row} must be a number greater than 0.");
                            continue;
                        }

                        var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                        if (drillHole == null)
                        {
                            errors.Add($"DrillHole '{drillHoleNameFile}' at row {row} not found.");
                            continue;
                        }

                        // Check for duplicate depth in database for the same drillhole and type combination
                        if (input.ImportMode == ImportMode.Add &&
                            existingDepths.TryGetValue((drillHole.Id, typeText), out var depths))
                        {
                            if (depths.Any(d => Math.Abs(d - depthValue) < 0.001))
                            {
                                errors.Add(
                                    $"Duplicate depth value {depthValue} found at row {row} for drillhole '{drillHoleNameFile}' and type '{typeText}'.");
                            }
                        }
                    }

                    break;
                }
                case ".csv":
                {
                    stream.Position = 0; // Reset stream position
                    using var reader = new StreamReader(stream);

                    // Get header row and find column indices
                    var headers = (await reader.ReadLineAsync())?.Split(',').Select(h => h.Trim()).ToList();
                    if (headers == null || headers.Count == 0)
                    {
                        errors.Add("CSV file has no headers.");
                        return errors;
                    }

                    var drillHoleColIndex = headers.FindIndex(h => h == drillHoleField.FileColumnName);
                    var depthColIndex = headers.FindIndex(h => h == depthField.FileColumnName);
                    var typeColIndex = headers.FindIndex(h => h == typeField.FileColumnName);

                    if (drillHoleColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{drillHoleField.FileColumnName}' in CSV file.");
                        return errors;
                    }

                    if (depthColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{depthField.FileColumnName}' in CSV file.");
                        return errors;
                    }

                    if (typeColIndex == -1)
                    {
                        errors.Add(
                            $"Could not find columns '{typeField.FileColumnName}' in Excel file.");
                        return errors;
                    }

                    var row = 2;
                    while (await reader.ReadLineAsync() is { } line)
                    {
                        var values = line.Split(',').Select(v => v.Trim()).ToArray();

                        var drillHoleNameFile = values[drillHoleColIndex];
                        var depthText = values[depthColIndex];
                        var typeText = values[typeColIndex];

                        if (!downholeSurveyTypeNames.Contains(typeText))
                        {
                            errors.Add($"Downhole Survey Type {typeText} not found.");
                            continue;
                        }

                        if (!double.TryParse(depthText, out var depthValue) || depthValue < 0)
                        {
                            errors.Add($"Depth value at row {row} must be a number greater than 0.");
                            row++;
                            continue;
                        }

                        var drillHole = drillHoles.FirstOrDefault(x => x.Name == drillHoleNameFile);
                        if (drillHole == null)
                        {
                            errors.Add($"DrillHole '{drillHoleNameFile}' at row {row} not found.");
                            row++;
                            continue;
                        }

                        // Check for duplicate depth in database for the same drillhole and type combination
                        if (input.ImportMode == ImportMode.Add &&
                            existingDepths.TryGetValue((drillHole.Id, typeText), out var depths))
                        {
                            if (depths.Any(d => Math.Abs(d - depthValue) < 0.001))
                            {
                                errors.Add(
                                    $"Duplicate depth value {depthValue} found at row {row} for drillhole '{drillHoleNameFile}' and type '{typeText}'.");
                            }
                        }

                        row++;
                    }

                    break;
                }
                default:
                {
                    errors.Add("Unsupported file format. Please upload a .xlsx or .csv file.");
                    break;
                }
            }
        }

        return errors.Count != 0 ? errors : [];
    }


    /// <inheritdoc />
    public async Task CalculateDesurveyAsync(CalculateDesurveyDto input)
    {
        var drillHole = await _drillHoleRepository.FirstOrDefaultAsync(x => x.Id == input.DrillHoleId);
        if (drillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);
        }
        
        var surveyType = await _downholeSurveyTypeRepository.GetAll()
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.TenantId == _abpSession.GetTenantId() && x.IsDefault);
        if (surveyType == null)
        {
            throw new UserFriendlyException("Could not find the default Survey Type.");
        }
            
        var surveys = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId && x.Type.Equals(surveyType.Name))
            .OrderBy(s => s.Depth)
            .ToListAsync();
        
        // var collarSurvey = new DownholeSurvey
        // {
        //     Depth = 0,
        //     Dip = surveys[0].Dip,
        //     Azimuth = surveys[0].Azimuth,
        //     Type = surveyType.Name,
        // };
        // surveys.Insert(0, collarSurvey);
        
        var surveyPoints = new List<DesurveyResult>();
        var currentNorth = (double)(drillHole.Northing ?? 0);
        var currentEast = (double)(drillHole.Easting ?? 0);
        var currentElev = (double)(drillHole.Elevation ?? 0);

        for (var i = 0; i < surveys.Count - 1; i++)
        {
            var prevSurvey = surveys[i];
            var nextSurvey = surveys[i + 1];
            var startDepth = prevSurvey.Depth;
            var endDepth = nextSurvey.Depth;
            const double step = 0.5;

            for (var depth = startDepth; depth <= endDepth; depth += step)
            {
                var ratio = (depth - startDepth) / (endDepth - startDepth);
                var dip = prevSurvey.Dip + (nextSurvey.Dip - prevSurvey.Dip) * ratio;
                var azimuth = prevSurvey.Azimuth + (nextSurvey.Azimuth - prevSurvey.Azimuth) * ratio;

                var sinDip = Math.Sin((dip * Math.PI / 180));
                var cosDip = Math.Cos((dip * Math.PI / 180));
                var cosAzimuth = Math.Cos((azimuth * Math.PI / 180));
                var sinAzimuth = Math.Sin((azimuth * Math.PI / 180));

                var deltaNorth = step * sinDip * cosAzimuth;
                var deltaEast = step * sinDip * sinAzimuth;
                var deltaElev = step * cosDip;
                
                currentNorth += deltaNorth;
                currentEast += deltaEast;
                currentElev += deltaElev;
                
                surveyPoints.Add(new DesurveyResult
                {
                    DrillHoleId = drillHole.Id,
                    Depth = double.Round(depth, 2),
                    Northing = double.Round(currentNorth, 3),
                    Easting = double.Round(currentEast, 3),
                    Elevation = double.Round(currentElev, 3),
                });
            }
        }
        
        await _desurveyResultRepository.InsertRangeAsync(surveyPoints);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DesurveyResultDto>> GetAllDesurveyResultAsync(PagedDesurveyResultRequestDto input)
    {
        var query = _desurveyResultRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();

        var desurveyResults = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<DesurveyResultDto>(totalCount, _mapper.Map<List<DesurveyResultDto>>(desurveyResults));
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillHoleDto>> GetAllDesurveyResultByDrillHoleAsync(GetAllDesurveyResultByDrillHoleDto input)
    {
        var query = _drillHoleRepository.GetAllIncluding(x => x.DesurveyResults)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .Where( x => x.ProjectId == input.ProjectId)
            ;

        query = query.Where(x => x.IsActive).OrderBy(x => x.Name);

        var queryProjectActive = query.Where(x => x.Project.IsActive == true && x.Prospect.IsActive == true);

        var totalCount = await queryProjectActive.CountAsync();

        var drillHole = await queryProjectActive
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var drillHoleDto = _mapper.Map<List<DrillHoleDto>>(drillHole);

        return new PagedResultDto<DrillHoleDto>(totalCount, drillHoleDto);
    }

    private async Task AddAndUpdateDownholeSurveyDataAsync(List<DownholeSurvey> dataToUpdate,
        List<DownholeSurvey> dataToInsert)
    {
        using (var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
        {
            try
            {
                var dbContext = await _dbContextProvider.GetDbContextAsync();

                // First update existing records
                if (dataToUpdate.Count != 0)
                {
                    await dbContext.BulkUpdateAsync(dataToUpdate);
                }

                // Then insert new records
                if (dataToInsert.Count != 0)
                {
                    await dbContext.BulkInsertAsync(dataToInsert);
                }

                await transaction.CompleteAsync();
            }
            catch (DbUpdateException dbEx)
            {
                throw new Exception("Downhole survey data operation failed", dbEx);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while processing downhole survey data", ex);
            }
        }
    }

    private async Task UpdateDownholeSurveyDataAsync(List<DownholeSurvey> surveyData)
    {
        using (var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
        {
            try
            {
                var dbContext = await _dbContextProvider.GetDbContextAsync();
                await dbContext.BulkUpdateAsync(surveyData);
                await transaction.CompleteAsync();
            }
            catch (DbUpdateException dbEx)
            {
                throw new Exception("Downhole survey data update failed", dbEx);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while updating downhole survey data", ex);
            }
        }
    }

    private async Task InsertDownholeSurveyDataAsync(List<DownholeSurvey> surveyData)
    {
        using (var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
        {
            try
            {
                var dbContext = await _dbContextProvider.GetDbContextAsync();
                await dbContext.BulkInsertAsync(surveyData);
                await transaction.CompleteAsync();
            }
            catch (DbUpdateException dbEx)
            {
                throw new Exception("Downhole survey data insert failed", dbEx);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while inserting downhole survey data", ex);
            }
        }
    }

    private async Task DeleteDownholeSurveyDataAsync(int projectId, List<int> drillHoleIds)
    {
        using (var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew))
        {
            try
            {
                var dbContext = await _dbContextProvider.GetDbContextAsync();
                if (drillHoleIds.Count != 0)
                {
                    await dbContext.Database.ExecuteSqlRawAsync(
                        "DELETE FROM public.\"AbpDownholeSurveys\" WHERE \"ProjectId\" = @projectId AND \"DrillHoleId\" = ANY(@drillHoleIds)",
                        new Npgsql.NpgsqlParameter("@projectId", projectId),
                        new Npgsql.NpgsqlParameter("@drillHoleIds", drillHoleIds.ToArray())
                    );
                }
                else
                {
                    await dbContext.Database.ExecuteSqlRawAsync(
                        "DELETE FROM public.\"AbpDownholeSurveys\" WHERE \"ProjectId\" = {0}",
                        projectId
                    );
                }

                await transaction.CompleteAsync();
            }
            catch (DbUpdateException dbEx)
            {
                throw new Exception("Downhole survey data delete failed", dbEx);
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while deleting downhole survey data", ex);
            }
        }
    }
}