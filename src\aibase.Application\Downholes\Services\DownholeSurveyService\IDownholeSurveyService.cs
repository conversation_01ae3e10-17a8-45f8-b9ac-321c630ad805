using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.Downholes.Dto;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoles.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Downholes.Services.DownholeSurveyService;

/// <inheritdoc />
public interface IDownholeSurveyService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DownholeSurveyDto>> GetDownholeSurveyDataAsync(PagedDownholeSurveyResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<int> UploadDownholeSurveyDataAsync([FromForm] UploadDownholeSurveyDataDto input, bool isDelete = true);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> UpdateDownholeSurveyDataAsync([FromForm] UploadDownholeSurveyDataDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> AddAndUpdateDownholeSurveyDataAsync([FromForm] UploadDownholeSurveyDataDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<string>> ValidationDownholeSurveyDataAsync([FromForm] UploadDownholeSurveyDataDto input);

            
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CalculateDesurveyAsync(CalculateDesurveyDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DesurveyResultDto>> GetAllDesurveyResultAsync(PagedDesurveyResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DrillHoleDto>> GetAllDesurveyResultByDrillHoleAsync(GetAllDesurveyResultByDrillHoleDto input);
}