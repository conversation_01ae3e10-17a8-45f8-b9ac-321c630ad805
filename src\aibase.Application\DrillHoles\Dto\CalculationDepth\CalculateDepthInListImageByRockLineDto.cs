using System.Collections.Generic;
using aibase.RockLines.Dto;

namespace aibase.DrillHoles.Dto.CalculationDepth;

/// <summary>
/// 
/// </summary>
public class CalculateDepthInListImageByRockLineDto
{
    /// <summary>
    /// 
    /// </summary>
    public List<RockLineDto> RockLines { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }
}