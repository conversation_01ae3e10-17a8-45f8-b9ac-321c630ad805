using System.Collections.Generic;
using aibase.RockLines.Dto;

namespace aibase.DrillHoles.Dto.CalculationDepth;

/// <summary>
/// 
/// </summary>
public class CroppedImageViewCalcDepthDto
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? Coordinate { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? Type { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<RockLineDto> RockLines { get; set; } = [];
}