﻿using System;
using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoleEntity;
using AutoMapper;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.DrillHoles.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(DrillHole))]
    public class DrillHoleDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        ///
        /// </summary>
        public DrillHoleStatus DrillHoleStatus { get; set; }

        /// <summary>
        /// Height above sea level
        /// </summary>
        public decimal? Elevation { get; set; }

        /// <summary>
        /// Y coordinate in projected coordinate system
        /// </summary>
        public decimal? Northing { get; set; }

        /// <summary>
        /// X coordinate in projected coordinate system
        /// </summary>
        public decimal? Easting { get; set; }

        /// <summary>
        /// Geographic longitude coordinate
        /// </summary>
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Geographic latitude coordinate
        /// </summary>
        public decimal? Latitude { get; set; }

        /// <summary>
        /// Angle of the drill hole from horizontal
        /// </summary>
        public decimal? Dip { get; set; }

        /// <summary>
        /// Direction angle from true north
        /// </summary>
        public decimal? Azimuth { get; set; }

        /// <summary>
        ///
        /// </summary>
        public double MaxDepth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProjectDto Project { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProspectDto Prospect { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int OriginalImages { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int CroppedRows { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsExport { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsExist { get; set; }
        
        public List<DesurveyResultDto> DesurveyResults { get; set; } = [];

        /// <summary>
        /// 
        /// </summary>
        public DateTime CreationTime { get; set; }
    }
}