using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using aibase.ImageCrops;
using aibase.ImageEntity;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageTypes.Dto;
using aibase.IsolationTenant;
using aibase.ProjectImageTypes;
using aibase.Prospects;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.DrillHoles.Services.DrillHoleService;

/// <inheritdoc />
public class DrillHoleService : IDrillHoleService
{
    private readonly IRepository<DrillHole, int> _repository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<ImageCrop, int> _imageCropRepository;
    private readonly IRepository<Prospect, int> _prospectRepository;
    private readonly IRepository<ProjectImageType, int> _projectImageTypeRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly ITenantEntityHelper _tenantEntityHelper;

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleService(
        IRepository<DrillHole, int> repository,
        IRepository<Image, int> imageRepository,
        IRepository<ImageCrop, int> imageCropRepository,
        IRepository<Prospect, int> prospectRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper,
        ITenantEntityHelper tenantEntityHelper,
        IRepository<ProjectImageType, int> projectImageTypeRepository)
    {
        _repository = repository;
        _imageRepository = imageRepository;
        _imageCropRepository = imageCropRepository;
        _prospectRepository = prospectRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _tenantEntityHelper = tenantEntityHelper;
        _projectImageTypeRepository = projectImageTypeRepository;
    }

    /// <inheritdoc />
    public async Task<DrillHoleDto> CreateAsync(CreateDrillHoleDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        await _tenantEntityHelper.ValidateEntityAsync(input);

        var existingDrillHole =
            await _repository.FirstOrDefaultAsync(d =>
                d.TenantId == _abpSession.TenantId && d.Name.ToLower() == input.Name.ToLower());
        if (existingDrillHole != null)
        {
            var existingDrillHoleExisting = _mapper.Map<DrillHoleDto>(existingDrillHole);
            existingDrillHoleExisting.IsExist = true;
            return existingDrillHoleExisting;
        }

        var drillHole = new DrillHole
        {
            Name = input.Name.Trim(),
            DrillHoleStatus = input.DrillHoleStatus,
            MaxDepth = input.MaxDepth,
            ProjectId = input.ProjectId,
            ProspectId = input.ProspectId,
            IsExport = false,
            IsActive = true,
            Azimuth = input.Azimuth,
            Dip = input.Dip,
            Easting = input.Easting,
            Elevation = input.Elevation,
            Northing = input.Northing,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(drillHole);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        var drillHoleDto = _mapper.Map<DrillHoleDto>(drillHole);
        drillHoleDto.IsExist = false;
        return drillHoleDto;
    }

    /// <inheritdoc />
    public async Task<DrillHoleDto> UpdateAsync(UpdateDrillHoleDto input)
    {
        var drillHole = await ValidateDrillHoleEntity(input.Id);

        var updateImages = (input.ProjectId != drillHole.ProjectId || input.ProspectId != drillHole.ProspectId);

        // Check if both ProjectId and ProspectId are being updated
        if (updateImages)
        {
            // Verify that the ProspectId belongs to the ProjectId
            var prospect = await _prospectRepository.GetAll()
                .FirstOrDefaultAsync(p => p.Id == input.ProspectId && p.TenantId == _abpSession.GetTenantId());

            if (prospect == null)
            {
                throw new UserFriendlyException(
                    $"Prospect {input.ProspectId} does not belong to Project {input.ProjectId}");
            }
        }

        // Update drill hole properties
        drillHole.Name = input.Name ?? drillHole.Name;
        drillHole.DrillHoleStatus = input.DrillHoleStatus ?? drillHole.DrillHoleStatus;
        drillHole.Elevation = input.Elevation;
        drillHole.Northing = input.Northing;
        drillHole.Easting = input.Easting;
        drillHole.Longitude = input.Longitude;
        drillHole.Latitude = input.Latitude;
        drillHole.Dip = input.Dip;
        drillHole.Azimuth = input.Azimuth;
        drillHole.MaxDepth = input.MaxDepth ?? drillHole.MaxDepth;
        drillHole.ProjectId = input.ProjectId;
        drillHole.ProspectId = input.ProspectId;
        drillHole.IsActive = input.IsActive ?? drillHole.IsActive;

        // Update associated images if ProjectId or ProspectId changed
        if (updateImages)
        {
            var images = await _imageRepository.GetAll()
                .Where(i => i.DrillHoleId == input.Id)
                .ToListAsync();

            foreach (var image in images)
            {
                image.ProjectId = input.ProjectId;
                image.ProspectId = input.ProspectId;
            }
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillHoleDto>> GetAllAsync(PagedDrillHoleResultRequestDto input)
    {
        int[] projectIds = [];
        int[] prospectIds = [];
        if (!string.IsNullOrEmpty(input.ProjectIds))
        {
            try
            {
                projectIds = JsonConvert.DeserializeObject<int[]>(input.ProjectIds) ?? [];
            }
            catch (JsonException ex)
            {
                throw new ArgumentException("Invalid ProjectIds format", nameof(input.ProjectIds), ex);
            }
        }

        if (!string.IsNullOrEmpty(input.ProspectIds))
        {
            try
            {
                prospectIds = JsonConvert.DeserializeObject<int[]>(input.ProspectIds) ?? [];
            }
            catch (JsonException ex)
            {
                throw new ArgumentException("Invalid ProspectIds format", nameof(input.ProspectIds), ex);
            }
        }

        var query = _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(projectIds.Length != 0, x => projectIds.Contains(x.ProjectId))
            .WhereIf(prospectIds.Length != 0, x => prospectIds.Contains(x.ProspectId))
            .WhereIf(input.DrillHoleStatus.HasValue, x => x.DrillHoleStatus == input.DrillHoleStatus);

        var queryProjectActive = query.Where(x => x.Project.IsActive == true && x.Prospect.IsActive == true);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            queryProjectActive = queryProjectActive.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            queryProjectActive = queryProjectActive.ApplySorting("Project.Name", "asc", r => r.CreationTime);
            queryProjectActive = queryProjectActive.OrderBy(x => x.Name); // Default sorting
        }

        var totalCount = await queryProjectActive.CountAsync();

        var drillHole = await queryProjectActive
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var drillHoleDto = _mapper.Map<List<DrillHoleDto>>(drillHole);

        return new PagedResultDto<DrillHoleDto>(totalCount, drillHoleDto);
    }

    /// <inheritdoc />
    public async Task<DrillHoleDto> GetAsync(EntityDto<int> input)
    {
        var drillHole = await ValidateDrillHoleEntity(input.Id);
        return _mapper.Map<DrillHoleDto>(drillHole);
    }

    /// <inheritdoc />
    public async Task<DrillHoleDto> GetAsync(string name)
    {
        var drillHole = await _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .FirstOrDefaultAsync(x => x.Name == name);

        return _mapper.Map<DrillHoleDto>(drillHole);
    }

    /// <inheritdoc />
    public async Task UpdateTotalImageAsync()
    {
        if (_abpSession.TenantId != null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var drillHoles = await _repository.GetAll()
            .ToListAsync();
        var drillHoleIds = drillHoles.Select(d => d.Id).ToList();

        var allImages = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(i => drillHoleIds.Contains(i.DrillHoleId))
            .ToListAsync();

        var allImageCrops = await _imageCropRepository.GetAll()
            .Where(c => allImages.Select(i => i.Id).Contains(c.ImageId))
            .ToListAsync();

        foreach (var drillHole in drillHoles)
        {
            var images = allImages.Where(i => i.DrillHoleId == drillHole.Id).ToList();
            var imageCrops = allImageCrops.Where(c => images.Select(i => i.Id).Contains(c.ImageId)).ToList();

            var originalImagesCount = images.Count;
            var croppedRowCount = imageCrops.Count(c => c.Type == ImageConstSettings.ImageCropRow);

            drillHole.OriginalImages = originalImagesCount;
            drillHole.CroppedRows = croppedRowCount;
        }
    }

    /// <inheritdoc />
    public async Task UpdateTotalImageByDrillholeAsync(int id)
    {
        var drillHole = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (drillHole != null)
        {
            var allImages = await _imageRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.DrillHoleId == id)
                .ToListAsync();
            var allImageCrops = await _imageCropRepository.GetAll()
                .Where(c => allImages.Select(i => i.Id).Contains(c.ImageId))
                .ToListAsync();

            var images = allImages.Where(i => i.DrillHoleId == drillHole.Id).ToList();
            var imageCrops = allImageCrops.Where(c => images.Select(i => i.Id).Contains(c.ImageId)).ToList();

            var originalImagesCount = images.Count;
            var croppedRowCount = imageCrops.Count(c =>
                c.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower);

            drillHole.OriginalImages = originalImagesCount;
            drillHole.CroppedRows = croppedRowCount;
        }
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillHoleDto>> GetDrillHoleByProjectProspectAsync(
        GetDrillHoleByProjectProspectDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.ProjectId.HasValue, x => x.ProjectId == input.ProjectId)
            .WhereIf(input.ProspectId.HasValue, x => x.ProspectId == input.ProspectId);

        var totalCount = await query.CountAsync();

        return new PagedResultDto<DrillHoleDto>(totalCount, _mapper.Map<List<DrillHoleDto>>(query.ToList()));
    }

    /// <inheritdoc />
    public async Task<DrillHoleStatusDto> GetDrillHoleStatusAsync(EntityDto<int> input)
    {
        var drillHole = await _repository.GetAll()
            .AsNoTracking()
            .Include(x => x.Project)
            .Include(x => x.Prospect)
            .FirstOrDefaultAsync(x => x.Id == input.Id && x.TenantId == _abpSession.GetTenantId());

        if (drillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.Id);
        }

        var images = await _imageRepository.GetAllIncluding(x => x.ImageType, x => x.ImageSubtype)
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.Id && x.ImageType != null && x.ImageType.IsStandard)
            .ToListAsync();

        return new DrillHoleStatusDto
        {
            DrillHole = _mapper.Map<DrillHoleDto>(drillHole),
            TotalStandard = images.Count,
            TotalWet = images.Count(x => x.ImageSubtype is { IsWet: true }),
            TotalDry = images.Count(x => x.ImageSubtype is { IsDry: true }),
            TotalUv = images.Count(x => x.ImageSubtype is { IsUv: true }),
        };
    }

    /// <inheritdoc />
    public async Task<int> MergeImageDrillHoleAsync(MergeDrillHoleDto input)
    {
        var masterDrillHole = await _repository.FirstOrDefaultAsync(x => x.Id == input.MasterDrillHoleId);
        if (masterDrillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.MasterDrillHoleId);
        }

        var sourceDrillHole = await _repository.FirstOrDefaultAsync(x => x.Id == input.SourceDrillHoleId);
        if (sourceDrillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.SourceDrillHoleId);
        }

        if (input.IsInactivate is true)
        {
            sourceDrillHole.IsActive = false;
        }

        var masterImages = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.MasterDrillHoleId)
            .ToListAsync();

        var sourceImages = await _imageRepository.GetAll()
            .Where(x => x.DrillHoleId == input.SourceDrillHoleId)
            .ToListAsync();

        if (masterImages.Count == 0 || sourceImages.Count == 0)
        {
            return masterImages.Count;
        }

        var masterProjectId = masterImages.First().ProjectId;
        var masterProspectId = masterImages.First().ProspectId;
        var masterDrillHoleId = masterImages.First().DrillHoleId;

        foreach (var img in sourceImages)
        {
            img.ProjectId = masterProjectId;
            img.ProspectId = masterProspectId;
            img.DrillHoleId = masterDrillHoleId;
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        var images = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(i => i.DrillHoleId == masterDrillHole.Id)
            .ToListAsync();
        var count = images.Count;

        var imageCrops = await _imageCropRepository.GetAll()
            .Where(c => images.Select(i => i.Id).Contains(c.ImageId))
            .ToListAsync();

        masterDrillHole.OriginalImages = count;
        masterDrillHole.CroppedRows = imageCrops.Count(c =>
            c.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower);

        return count;
    }

    /// <inheritdoc />
    public async Task<bool> CheckOverlapDrillHoleAsync(CheckOverlapDrillHoleDto input)
    {
        var masterImages = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.MasterDrillHoleId)
            .ToListAsync();

        var sourceImages = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.SourceDrillHoleId)
            .ToListAsync();

        if (masterImages.Count == 0 || sourceImages.Count == 0)
        {
            return false;
        }

        var depthFromMaster = masterImages.Min(x => x.DepthFrom);
        var depthToMaster = masterImages.Max(x => x.DepthTo);

        var depthFromSource = sourceImages.Min(x => x.DepthFrom);
        var depthToSource = sourceImages.Max(x => x.DepthTo);

        return depthFromMaster < depthToSource && depthFromSource < depthToMaster;
    }

    /// <inheritdoc />
    public async Task<DrillHoleCheckProspectResultDto> DrillHoleCheckProspectAsync(DrillHoleCheckProspectDto input)
    {
        var result = new DrillHoleCheckProspectResultDto
        {
            DrillHoleTrues = [],
            DrillHoleFalses = []
        };

        // Get all drill holes with matching names for the current tenant
        var drillHoles = await _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.TenantId == _abpSession.GetTenantId() &&
                        input.DrillHoleNames.Contains(x.Name))
            .ToListAsync();

        // Check each requested drill hole name
        foreach (var name in input.DrillHoleNames)
        {
            var drillHole = drillHoles.FirstOrDefault(x => x.Name == name);

            // If drill hole exists and belongs to the prospect, add to true list
            if (drillHole != null && drillHole.ProspectId == input.ProspectId)
            {
                result.DrillHoleTrues.Add(name);
            }
            else
            {
                result.DrillHoleFalses.Add(name);
            }
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<List<TotalImageTypeByDrillHoleDto>> GetTotalImageTypeByDrillHoleAsync(int drillHoleId)
    {
        var drillHole = await _repository.FirstOrDefaultAsync(x => x.Id == drillHoleId);
        if (drillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), drillHoleId);
        }

        var projectId = drillHole.ProjectId;

        var images = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == drillHoleId)
            .ToListAsync();

        var imageTypes = await _projectImageTypeRepository.GetAllIncluding(x => x.ImageType)
            .AsNoTracking()
            .Where(x => x.ProjectId == projectId)
            .Select(x => x.ImageType)
            .ToListAsync();

        var totals = new List<TotalImageTypeByDrillHoleDto>();
        foreach (var imageType in imageTypes)
        {
            totals.Add(new TotalImageTypeByDrillHoleDto
            {
                ImageType = new ImageTypeDto { Id = imageType.Id, Name = imageType.Name },
                Total = images.Count(x => x.ImageTypeId == imageType.Id)
            });
        }

        return totals;
    }

    private async Task<DrillHole> ValidateDrillHoleEntity(int id)
    {
        var drillHole = await _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .FirstOrDefaultAsync(x => x.Id == id && x.TenantId == _abpSession.GetTenantId());

        if (drillHole == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), id);
        }

        return drillHole;
    }
}