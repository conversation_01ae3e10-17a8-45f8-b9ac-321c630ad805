using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.DrillHoles.Dto;

namespace aibase.DrillHoles.Services.DrillHoleService;

/// <inheritdoc />
public interface IDrillHoleService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleDto> CreateAsync(CreateDrillHoleDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleDto> UpdateAsync(UpdateDrillHoleDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DrillHoleDto>> GetAllAsync(PagedDrillHoleResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<DrillHoleDto> GetAsync(string name);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task UpdateTotalImageAsync();

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task UpdateTotalImageByDrillholeAsync(int id);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DrillHoleDto>> GetDrillHoleByProjectProspectAsync(GetDrillHoleByProjectProspectDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleStatusDto> GetDrillHoleStatusAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> MergeImageDrillHoleAsync(MergeDrillHoleDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckOverlapDrillHoleAsync(CheckOverlapDrillHoleDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleCheckProspectResultDto> DrillHoleCheckProspectAsync(DrillHoleCheckProspectDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="drillHoleId"></param>
    /// <returns></returns>
    Task<List<TotalImageTypeByDrillHoleDto>> GetTotalImageTypeByDrillHoleAsync(int drillHoleId);
}