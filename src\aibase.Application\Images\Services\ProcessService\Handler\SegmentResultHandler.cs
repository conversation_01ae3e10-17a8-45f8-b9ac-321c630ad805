using System;
using System.Collections.Generic;
using System.Linq;
using aibase.Images.Dto;
using aibase.Models.Dto;

namespace aibase.Images.Services.ProcessService.Handler;

/// <summary>
/// 
/// </summary>
public static class SegmentResultHandler
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="rawDto"></param>
    /// <returns></returns>
    public static List<SegmentResultDto> ConvertSegmentResult(SegmentResult rawDto)
    {
        var resultDto = new List<SegmentResultDto>();
        var index = 0;
        var segmentIndicesCount = rawDto.SegmentIndices.Length;

        foreach (var contour in rawDto.MaskContourPoints)
        {
            var dto = new SegmentResultDto
            {
                name = $"polygon-{Guid.NewGuid()}",
                isPolyComplete = true,
                points = [],
                color = GetRandomColor(),
                Class = rawDto.SegmentType[index],
                rowIndex = index < segmentIndicesCount ? rawDto.SegmentIndices[index] : 0
            };

            foreach (var point in contour.Where(point => point.Count == 2))
            {
                dto.points.Add([point[0], point[1]]);
            }

            resultDto.Add(dto);
            index++;
        }

        return resultDto;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="rawDto"></param>
    /// <returns></returns>
    public static List<SegmentResultDto> ConvertSegmentDetailResult(List<RefinedSegmentation> rawDto)
    {
        return rawDto.Select(x => new SegmentResultDto
        {
            name = $"polygon-{Guid.NewGuid()}",
            isPolyComplete = true,
            points = x.Points.Select(p => p).ToList(),
            color = GetRandomColor(),
            Class = x.Class,
            rowIndex = x.RowIndex,
        }).ToList();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="segments"></param>
    /// <returns></returns>
    public static List<SegmentResultDto> ComputeBoundingAndIntersection(List<SegmentResultDto> segments)
    {
        if (segments.Count == 0)
            return []; // Return an empty list if input is null or empty

        foreach (var segment in segments)
        {
            if (segment.points.Count < 2)
            {
                segment.BoundingSegment = new CoordinateV2Dto(); // Initialize empty bounding box
                segment.IntersectionPoints = new LineCoordinateDto(); // Initialize empty intersection points
                continue;
            }

            // Compute bounding box
            var minX = segment.points.Min(p => p[0]);
            var maxX = segment.points.Max(p => p[0]);
            var minY = segment.points.Min(p => p[1]);
            var maxY = segment.points.Max(p => p[1]);

            segment.BoundingSegment =  new CoordinateV2Dto
            {
                X = minX,
                Y = minY,
                Width = maxX - minX,
                Height = maxY - minY
            };

            // Compute horizontal middle line
            var midY = minY + (maxY - minY) / 2;

            // Find intersection points
            var intersectionX = new List<double>();

            for (var i = 0; i < segment.points.Count; i++)
            {
                var p1 = segment.points[i];
                var p2 = segment.points[(i + 1) % segment.points.Count]; 

                if (!(((p1[1] - midY) * (p2[1] - midY)) <= 0) || p1[1] == p2[1])
                    continue;

                var xIntersect = p1[0] + (midY - p1[1]) * (p2[0] - p1[0]) / (p2[1] - p1[1]);
                intersectionX.Add(xIntersect);
            }

            // Ensure we found exactly two intersection points
            if (intersectionX.Count >= 2)
            {
                intersectionX.Sort();

                var startX = intersectionX.First();
                var endX = intersectionX.Last();

                segment.IntersectionPoints = new LineCoordinateDto
                {
                    StartX = startX,
                    StartY = midY,
                    EndX = endX,
                    EndY = midY,
                    Y = midY
                };
            }
            else
            {
                segment.IntersectionPoints = new LineCoordinateDto(); // No valid intersection
            }
        }

        return segments;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public static string GetRandomColor()
    {
        var random = new Random();
        var randomIndex = random.Next(aibaseConsts.COLORS.Count);
        return aibaseConsts.COLORS[randomIndex];
    }
}