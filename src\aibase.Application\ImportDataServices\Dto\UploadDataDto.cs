using System.ComponentModel.DataAnnotations;
using aibase.ImportMappingTemplates;
using Microsoft.AspNetCore.Http;

namespace aibase.ImportDataServices.Dto;

/// <summary>
/// 
/// </summary>
public class UploadDataDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>

    public ImportFileType? ImportFileType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? SuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ImportMappingTemplateId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ImportMappingFields { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? CreateNewDrillHoles { get; set; }

    /// <summary>
    ///
    /// </summary>
    public ImportMode ImportMode { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public IFormFile ExcelFile { get; set; }
}

/// <summary>
/// 
/// </summary>
public enum ImportMode
{
    /// <summary>
    /// 
    /// </summary>
    Add = 1,

    /// <summary>
    /// 
    /// </summary>
    Update,

    /// <summary>
    /// 
    /// </summary>
    AddAndUpdate,
}