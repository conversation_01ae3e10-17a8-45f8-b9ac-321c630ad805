using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.MobileProfiles.Dto;
using aibase.MobileProfiles.Services;

namespace aibase.MobileProfiles
{
    /// <summary>
    /// Mobile Profile application service
    /// </summary>
    [AbpAuthorize]
    public class MobileProfileAppService : AsyncCrudAppService<MobileProfile, MobileProfileDto, int, PagedMobileProfileResultRequestDto, CreateMobileProfileDto, UpdateMobileProfileDto>, IMobileProfileAppService
    {
        private readonly IMobileProfileService _mobileProfileService;

        /// <summary>
        /// Constructor
        /// </summary>
        public MobileProfileAppService(
            IRepository<MobileProfile, int> repository,
            IMobileProfileService mobileProfileService) : base(repository)
        {
            _mobileProfileService = mobileProfileService;
        }

        /// <summary>
        /// Create a new mobile profile
        /// </summary>
        public override async Task<MobileProfileDto> CreateAsync(CreateMobileProfileDto input)
        {
            var mobileProfile = await _mobileProfileService.CreateAsync(input);
            return new MobileProfileDto
            {
                Id = mobileProfile.Id,
                Name = mobileProfile.Name,
                Description = mobileProfile.Description,
                IsActive = mobileProfile.IsActive,
                IsStandard = mobileProfile.IsStandard,
                IsWet = mobileProfile.IsWet,
                IsDry = mobileProfile.IsDry,
                IsUv = mobileProfile.IsUv,
                IsRig = mobileProfile.IsRig,
                MobileCameraType = mobileProfile.MobileCameraType,
                ExternalCameraType = mobileProfile.ExternalCameraType,
                IsDepthIncrement = mobileProfile.IsDepthIncrement,
                DepthIncrement = mobileProfile.DepthIncrement,
                IsApplyDepthIncrement = mobileProfile.IsApplyDepthIncrement,
                RotateImg = mobileProfile.RotateImg
            };
        }

        /// <summary>
        /// Update an existing mobile profile
        /// </summary>
        public override async Task<MobileProfileDto> UpdateAsync(UpdateMobileProfileDto input)
        {
            return await _mobileProfileService.UpdateAsync(input);
        }

        /// <inheritdoc />
        public override async Task<PagedResultDto<MobileProfileDto>> GetAllAsync(PagedMobileProfileResultRequestDto input)
        {
            return await _mobileProfileService.GetAllAsync(input);
        }

        /// <inheritdoc />
        public async override Task<MobileProfileDto> GetAsync(EntityDto<int> input)
        {
            return await _mobileProfileService.GetAsync(input);
        }

        /// <summary>
        /// Delete a mobile profile
        /// </summary>
        public override async Task DeleteAsync(EntityDto<int> input)
        {
            await _mobileProfileService.DeleteAsync(input.Id);
        }

        /// <inheritdoc />
        public async Task<MobileProfileDto> GetMobileProfileByProjectAsync(int projectId)
        {
            return await _mobileProfileService.GetMobileProfileByProjectAsync(projectId);
        }
    }
}

