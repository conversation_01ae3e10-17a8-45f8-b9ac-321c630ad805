using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes;
using aibase.ImageTypes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using aibase.MobileProfiles.Dto;
using aibase.ProjectEntity;
using aibase.Projects.Dto;

namespace aibase.MobileProfiles.Services;

/// <inheritdoc />
public class MobileProfileService : IMobileProfileService
{
    private readonly IRepository<MobileProfile, int> _repository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public MobileProfileService(
        IRepository<MobileProfile, int> repository,
        IRepository<Project, int> projectRepository,
        IRepository<ImageType, int> imageTypeRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _projectRepository = projectRepository;
        _imageTypeRepository = imageTypeRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<MobileProfile> CreateAsync(CreateMobileProfileDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingMobileProfile = await _repository
            .FirstOrDefaultAsync(x => x.Name == input.Name && x.TenantId == tenantId);

        if (existingMobileProfile != null)
        {
            if (returnExist)
            {
                return existingMobileProfile;
            }

            throw new UserFriendlyException(
                $"A Mobile Profile with the name {existingMobileProfile.Name} already exists.");
        }

        // Auto-correct business rule violations
        var correctedValues = ApplyBusinessRuleCorrections(input.IsStandard, input.IsRig, input.IsWet,
            input.IsDry, input.IsUv, input.MobileCameraType, input.ExternalCameraType);

        var mobileProfile = new MobileProfile
        {
            Name = input.Name,
            Description = input.Description,
            IsActive = input.IsActive,
            IsStandard = correctedValues.IsStandard,
            IsWet = correctedValues.IsWet,
            IsDry = correctedValues.IsDry,
            IsUv = correctedValues.IsUv,
            IsRig = correctedValues.IsRig,
            MobileCameraType = correctedValues.MobileCameraType,
            ExternalCameraType = correctedValues.ExternalCameraType,
            IsDepthIncrement = input.IsDepthIncrement,
            DepthIncrement = input.DepthIncrement,
            IsApplyDepthIncrement = input.IsApplyDepthIncrement,
            RotateImg = input.RotateImg,
            TenantId = tenantId
        };

        await _repository.InsertAsync(mobileProfile);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        // Handle project linking
        if (input.ProjectIds != null && input.ProjectIds.Count != 0)
        {
            await LinkProjectsToMobileProfile(mobileProfile.Id, input.ProjectIds);
        }

        return mobileProfile;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<MobileProfileDto>> GetAllAsync(PagedMobileProfileResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .Where(x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!string.IsNullOrWhiteSpace(input.Keyword),
                x => input.Keyword != null && (x.Name.Contains(input.Keyword) ||
                                               (x.Description != null && x.Description.Contains(input.Keyword))))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(x => x.Name);

        var totalCount = await query.CountAsync();

        var mobileProfiles = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<MobileProfileDto>(totalCount,
            _mapper.Map<List<MobileProfileDto>>(mobileProfiles));
    }

    /// <inheritdoc />
    public async Task<MobileProfileDto> UpdateAsync(UpdateMobileProfileDto input)
    {
        var mobileProfile = await ValidateMobileProfileEntity(input.Id);

        // Check for duplicate name if name is being changed
        if (!string.IsNullOrEmpty(input.Name) && input.Name != mobileProfile.Name)
        {
            var existingMobileProfile = await _repository.FirstOrDefaultAsync(x =>
                x.Name == input.Name &&
                x.TenantId == _abpSession.GetTenantId() &&
                x.Id != input.Id);

            if (existingMobileProfile != null)
            {
                throw new UserFriendlyException($"Mobile Profile with name '{input.Name}' already exists.");
            }
        }

        // Apply updates with null coalescing
        var updatedIsStandard = input.IsStandard ?? mobileProfile.IsStandard;
        var updatedIsRig = input.IsRig ?? mobileProfile.IsRig;
        var updatedIsWet = input.IsWet ?? mobileProfile.IsWet;
        var updatedIsDry = input.IsDry ?? mobileProfile.IsDry;
        var updatedIsUv = input.IsUv ?? mobileProfile.IsUv;
        var updatedMobileCameraType = input.MobileCameraType ?? mobileProfile.MobileCameraType;
        var updatedExternalCameraType = input.ExternalCameraType ?? mobileProfile.ExternalCameraType;

        // Auto-correct business rule violations with resolved values
        var correctedValues = ApplyBusinessRuleCorrections(updatedIsStandard, updatedIsRig, updatedIsWet,
            updatedIsDry, updatedIsUv, updatedMobileCameraType, updatedExternalCameraType);

        mobileProfile.Name = input.Name ?? mobileProfile.Name;
        mobileProfile.Description = input.Description ?? mobileProfile.Description;
        mobileProfile.IsActive = input.IsActive ?? mobileProfile.IsActive;
        mobileProfile.IsStandard = correctedValues.IsStandard;
        mobileProfile.IsWet = correctedValues.IsWet;
        mobileProfile.IsDry = correctedValues.IsDry;
        mobileProfile.IsUv = correctedValues.IsUv;
        mobileProfile.IsRig = correctedValues.IsRig;
        mobileProfile.MobileCameraType = correctedValues.MobileCameraType;
        mobileProfile.ExternalCameraType = correctedValues.ExternalCameraType;
        mobileProfile.IsDepthIncrement = input.IsDepthIncrement ?? mobileProfile.IsDepthIncrement;
        mobileProfile.DepthIncrement = input.DepthIncrement ?? mobileProfile.DepthIncrement;
        mobileProfile.IsApplyDepthIncrement = input.IsApplyDepthIncrement ?? mobileProfile.IsApplyDepthIncrement;
        mobileProfile.RotateImg = input.RotateImg ?? mobileProfile.RotateImg;

        await _repository.UpdateAsync(mobileProfile);

        // Handle project linking updates
        if (input.ProjectIds != null)
        {
            await UpdateProjectLinks(mobileProfile.Id, input.ProjectIds);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        return new MobileProfileDto
        {
            Id = mobileProfile.Id,
            Name = mobileProfile.Name,
            Description = mobileProfile.Description,
            IsActive = mobileProfile.IsActive,
            IsStandard = mobileProfile.IsStandard,
            IsWet = mobileProfile.IsWet,
            IsDry = mobileProfile.IsDry,
            IsUv = mobileProfile.IsUv,
            IsRig = mobileProfile.IsRig,
            MobileCameraType = mobileProfile.MobileCameraType,
            ExternalCameraType = mobileProfile.ExternalCameraType,
            IsDepthIncrement = mobileProfile.IsDepthIncrement,
            DepthIncrement = mobileProfile.DepthIncrement,
            IsApplyDepthIncrement = mobileProfile.IsApplyDepthIncrement,
            RotateImg = mobileProfile.RotateImg
        };
    }

    /// <inheritdoc />
    public async Task<MobileProfileDto> GetAsync(EntityDto<int> input)
    {
        var mobileProfile = await ValidateMobileProfileEntity(input.Id);

        return new MobileProfileDto
        {
            Id = mobileProfile.Id,
            Name = mobileProfile.Name,
            Description = mobileProfile.Description,
            IsActive = mobileProfile.IsActive,
            IsStandard = mobileProfile.IsStandard,
            IsWet = mobileProfile.IsWet,
            IsDry = mobileProfile.IsDry,
            IsUv = mobileProfile.IsUv,
            IsRig = mobileProfile.IsRig,
            MobileCameraType = mobileProfile.MobileCameraType,
            ExternalCameraType = mobileProfile.ExternalCameraType,
            IsDepthIncrement = mobileProfile.IsDepthIncrement,
            DepthIncrement = mobileProfile.DepthIncrement,
            IsApplyDepthIncrement = mobileProfile.IsApplyDepthIncrement,
            RotateImg = mobileProfile.RotateImg,
            Projects = mobileProfile.Projects.Select(x => new ProjectDto
            {
                Id = x.Id,
                Name = x.Name,
            }).ToList(),
        };
    }

    /// <inheritdoc />
    public async Task<MobileProfileDto> GetMobileProfileByProjectAsync(int projectId)
    {
        var project = await _projectRepository.GetAll()
            .Include(x => x.MobileProfile)
            .Include( x => x.ProjectImageTypes)
            .ThenInclude(x => x.ImageType)
            .ThenInclude(x => x.ImageSubtypes)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == projectId);
        if (project == null)
        {
            throw new UserFriendlyException($"Project with Id '{projectId}' does not exist.");
        }

        var mobileProfile = project.MobileProfile;
        if (mobileProfile == null)
        {
            throw new UserFriendlyException($"No Mobile Profile is linked to Project '{project.Name}'.");
        }

        var imageTypes = project.ProjectImageTypes.Select(x => x.ImageType).DistinctBy(x => x.Id).ToList();

        return new MobileProfileDto
        {
            Id = project.MobileProfile.Id,
            Name = project.MobileProfile.Name,
            Description = project.MobileProfile.Description,
            IsActive = project.MobileProfile.IsActive,
            IsStandard = project.MobileProfile.IsStandard,
            IsWet = project.MobileProfile.IsWet,
            IsDry = project.MobileProfile.IsDry,
            IsUv = project.MobileProfile.IsUv,
            IsRig = project.MobileProfile.IsRig,
            MobileCameraType = project.MobileProfile.MobileCameraType,
            ExternalCameraType = project.MobileProfile.ExternalCameraType,
            IsDepthIncrement = project.MobileProfile.IsDepthIncrement,
            DepthIncrement = project.MobileProfile.DepthIncrement,
            IsApplyDepthIncrement = project.MobileProfile.IsApplyDepthIncrement,
            RotateImg = project.MobileProfile.RotateImg,
            ImageTypes = imageTypes.Where(x => x.IsActive && (x.IsStandard || x.IsRig || x.IsRigCorrected)).Select(x => new ImageTypeDto
            {
                Id = x.Id,
                Name = x.Name,
                IsActive = x.IsActive,
                IsStandard = x.IsStandard,
                IsRigCorrected = x.IsRigCorrected,
                IsRig = x.IsRig,
                ImageSubtypes = x.ImageSubtypes.Where(i => i.IsActive && (i.IsDry || i.IsWet || i.IsDry)).Select(i => new ImageSubtypeDto
                {
                    Id = i.Id,
                    Name = i.Name,
                    IsActive = i.IsActive,
                    IsDry = i.IsDry,
                    IsWet = i.IsWet,
                    IsUv = i.IsUv,
                    ImageTypeId = i.ImageTypeId
                }).ToList(),
            }).ToList(),
        };
    }

    /// <inheritdoc />
    public async Task DeleteAsync(int id)
    {
        var mobileProfile = await ValidateMobileProfileEntity(id);

        // Handle orphaned projects by setting their MobileProfileId to null
        var linkedProjects = await _projectRepository
            .GetAllListAsync(p => p.MobileProfileId == id);

        foreach (var project in linkedProjects)
        {
            project.MobileProfileId = null;
            await _projectRepository.UpdateAsync(project);
        }

        await _repository.DeleteAsync(mobileProfile);
    }

    private async Task<MobileProfile> ValidateMobileProfileEntity(int id)
    {
        var mobileProfile = await _repository.GetAllIncluding(x => x.Projects).FirstOrDefaultAsync(x =>
            x.Id == id && x.TenantId == _abpSession.GetTenantId());

        if (mobileProfile == null)
        {
            throw new EntityNotFoundException(typeof(MobileProfile), id);
        }

        return mobileProfile;
    }

    private BusinessRuleCorrectedValues ApplyBusinessRuleCorrections(bool isStandard, bool isRig, bool isWet,
        bool isDry, bool isUv, CameraType mobileCameraType, CameraType externalCameraType)
    {
        var correctedValues = new BusinessRuleCorrectedValues
        {
            IsStandard = isStandard,
            IsRig = isRig,
            IsWet = isWet,
            IsDry = isDry,
            IsUv = isUv,
            MobileCameraType = mobileCameraType,
            ExternalCameraType = externalCameraType
        };

        // Rule 1: Ensure at least one of IsStandard or IsRig is true
        ApplyStandardRigRequirementCorrection(correctedValues);

        // Rule 2: Apply camera type corrections based on IsStandard and IsRig
        ApplyCameraTypeCorrections(correctedValues);

        // Rule 3: Apply condition flags corrections
        ApplyConditionFlagsCorrections(correctedValues);

        return correctedValues;
    }

    private static void ApplyStandardRigRequirementCorrection(BusinessRuleCorrectedValues values)
    {
        // If both IsStandard and IsRig are false, set IsStandard to true
        if (values is { IsStandard: false, IsRig: false })
        {
            throw new UserFriendlyException("At least one of IsStandard or IsRig must be true.");
        }
    }

    private static void ApplyCameraTypeCorrections(BusinessRuleCorrectedValues values)
    {
        // If only IsRig is true (IsStandard is false)
        if (values is { IsStandard: false, IsRig: true })
        {
            values.MobileCameraType = CameraType.Rig;
            values.ExternalCameraType = CameraType.Rig;
        }
        // If only IsStandard is true (IsRig is false)
        else if (values is { IsStandard: true, IsRig: false })
        {
            values.MobileCameraType = CameraType.Standard;
            values.ExternalCameraType = CameraType.Standard;
        }
        // When both IsStandard and IsRig are true, camera types can remain as they are
    }

    private static void ApplyConditionFlagsCorrections(BusinessRuleCorrectedValues values)
    {
        // If any of IsWet, IsDry, or IsUv is true but IsStandard is false, set IsStandard to true
        if ((values.IsWet || values.IsDry || values.IsUv) && !values.IsStandard)
        {
            values.IsStandard = true;
        }
        // If IsStandard is false, set IsWet, IsDry, and IsUv to false
        else if (!values.IsStandard)
        {
            values.IsWet = false;
            values.IsDry = false;
            values.IsUv = false;
        }
    }

    private class BusinessRuleCorrectedValues
    {
        public bool IsStandard { get; set; }
        public bool IsRig { get; set; }
        public bool IsWet { get; set; }
        public bool IsDry { get; set; }
        public bool IsUv { get; set; }
        public CameraType MobileCameraType { get; set; }
        public CameraType ExternalCameraType { get; set; }
    }

    private async Task LinkProjectsToMobileProfile(int mobileProfileId, List<int> projectIds)
    {
        var tenantId = _abpSession.GetTenantId();

        var projectIdsSet = projectIds.Distinct().ToList(); // Remove duplicates
        var projects = await _projectRepository.GetAllListAsync(p =>
            projectIdsSet.Contains(p.Id) && p.TenantId == tenantId);

        foreach (var project in projects)
        {
            project.MobileProfileId = mobileProfileId;
            await _projectRepository.UpdateAsync(project); // Marks as modified
        }
    }

    private async Task UpdateProjectLinks(int mobileProfileId, List<int> projectIds)
    {
        var tenantId = _abpSession.GetTenantId();

        // Get currently linked projects
        var currentlyLinkedProjects = await _projectRepository
            .GetAllListAsync(p => p.MobileProfileId == mobileProfileId && p.TenantId == tenantId);

        var currentlyLinkedIds = currentlyLinkedProjects.Select(p => p.Id).ToList();

        // Determine which projects to link and unlink
        var projectsToLink = projectIds.Except(currentlyLinkedIds).ToList();
        var projectsToUnlink = currentlyLinkedIds.Except(projectIds).ToList();

        // Link new projects
        foreach (var projectId in projectsToLink)
        {
            var project = await _projectRepository.FirstOrDefaultAsync(p =>
                p.Id == projectId && p.TenantId == tenantId);

            if (project != null)
            {
                project.MobileProfileId = mobileProfileId;
                await _projectRepository.UpdateAsync(project);
            }
        }

        // Unlink projects
        foreach (var projectId in projectsToUnlink)
        {
            var project = await _projectRepository.FirstOrDefaultAsync(p =>
                p.Id == projectId && p.TenantId == tenantId);

            if (project != null)
            {
                project.MobileProfileId = null;
                await _projectRepository.UpdateAsync(project);
            }
        }
    }
}