﻿using System.Threading.Tasks;
using System;
using Microsoft.AspNetCore.Mvc;
using aibase.Models.Dto;
using Abp.Authorization;
using Abp.UI;
using aibase.Models.Services.OcrService;
using aibase.Models.Services.SegmentService;
using aibase.StepWorkflowEntity;

namespace aibase.Models;

/// <summary>
///
///
/// </summary>
[AbpAuthorize]
public class ModelAppSerivce : aibaseAppServiceBase
{
    private readonly IOcrService _ocrService;
    private readonly ISegmentService _segmentService;

    /// <summary>
    ///
    /// </summary>
    public ModelAppSerivce(
        IOcrService ocrService,
        ISegmentService segmentService)
    {
        _ocrService = ocrService;
        _segmentService = segmentService;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public async Task<IActionResult> OcrImage([FromForm] OcrDto data)
    {
        if (data.Image == null && string.IsNullOrEmpty(data.Url))
        {
            return new BadRequestObjectResult("imageOrUrlIsRequire");
        }

        try
        {
            var imageStream = await OcrService.GetImageStream(data.Image, data.Url);

            var ocrResult = await _ocrService.PerformOcr(imageStream, null, null);

            if (ocrResult != null)
            {
                return new OkObjectResult(ocrResult);
            }

            return new OkObjectResult(null);
        }
        catch (UserFriendlyException ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
        catch (ArgumentException ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
    }

    /// <summary>
    /// Segment image
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    /// <exception cref="Exception"></exception>
    public async Task<IActionResult> SegmentAsync([FromForm] SegmentDto data)
    {
        if (data.Image == null && string.IsNullOrEmpty(data.Url))
        {
            return new BadRequestObjectResult("imageOrUrlIsRequire");
        }

        try
        {
            var segmentResult = await _segmentService.PerformSegment(data.Image, data.Url, [], BoundingRowOption.Polygon);

            if (segmentResult != null)
            {
                return new OkObjectResult(segmentResult);
            }

            return new OkObjectResult(null);
        }
        catch (ArgumentException ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
    }
}