using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Abp.Dependency;
using aibase.Images.Dto;
using aibase.Models.Dto;

namespace aibase.Models.Services.OcrService;

/// <inheritdoc />
public interface IOcrService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="imageStream"></param>
    /// <param name="cropCoordinate"></param>
    /// <param name="boundingRow"></param>
    /// <returns></returns>
    Task<List<OcrResultV2Dto>> PerformOcr(Stream imageStream, CropCoordinate? cropCoordinate, CropCoordinate[]? boundingRow);
}