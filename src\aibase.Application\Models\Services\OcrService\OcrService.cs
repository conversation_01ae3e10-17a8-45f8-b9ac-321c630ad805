﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using aibase.Configuration;
using aibase.Images.Dto;
using aibase.Models.Dto;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
using Microsoft.Extensions.Options;

namespace aibase.Models.Services.OcrService;

/// <inheritdoc />
public class OcrService : IOcrService
{
    private readonly IComputerVisionClient _clientComputer;
    private readonly ExternalServiceOptions _externalServiceOptions;

    /// <summary>
    ///
    /// </summary>
    /// <param name="externalServiceOptions"></param>
    public OcrService(
        IOptions<ExternalServiceOptions> externalServiceOptions)
    {
        _externalServiceOptions = externalServiceOptions.Value;
        _clientComputer =
            new ComputerVisionClient(
                new ApiKeyServiceClientCredentials(_externalServiceOptions.Ocr.SubscriptionKey))
            {
                Endpoint = _externalServiceOptions.Ocr.AzureEndpoint
            };
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="image"></param>
    /// <param name="url"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static async Task<Stream> GetImageStream(IFormFile? image, string url)
    {
        if (image is { Length: > 0 })
        {
            return image.OpenReadStream();
        }
        else if (!string.IsNullOrEmpty(url))
        {
            using var httpClient = new HttpClient();
            var response = await httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                throw new ArgumentException("urlInvalid");
            }

            return await response.Content.ReadAsStreamAsync();
        }
        else
        {
            throw new ArgumentException("imageInvalid");
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="imageStream"></param>
    /// <param name="cropCoordinate"></param>
    /// <param name="boundingRow"></param>
    /// <returns></returns>
    public async Task<List<OcrResultV2Dto>> PerformOcr(Stream imageStream, CropCoordinate? cropCoordinate,
        CropCoordinate[]? boundingRow)
    {
        var textResults = await ReadTextFromImageAsync(imageStream);
        var output = ExtractFilteredResults(textResults, cropCoordinate);
        if (boundingRow == null) return output;

        var resultsWithRowIndex = AssignRowIndex(output, boundingRow);

        return resultsWithRowIndex;
    }

    private async Task<ReadOperationResult> ReadTextFromImageAsync(Stream imageStream)
    {
        var textHeaders = await _clientComputer.ReadInStreamAsync(imageStream);
        var operationLocation = textHeaders.OperationLocation;
        var operationId = operationLocation[(operationLocation.LastIndexOf('/') + 1)..];

        ReadOperationResult results;
        do
        {
            results = await _clientComputer.GetReadResultAsync(Guid.Parse(operationId));
            await Task.Delay(1000);
        } while (results.Status is OperationStatusCodes.Running or OperationStatusCodes.NotStarted);

        return results;
    }

    private static List<OcrResultV2Dto> ExtractFilteredResults(ReadOperationResult results,
        CropCoordinate? cropCoordinate)
    {
        if (results.Status != OperationStatusCodes.Succeeded)
        {
            return null!;
        }

        var lines = results.AnalyzeResult.ReadResults.SelectMany(page => page.Lines);
        var output = new List<OcrResultV2Dto>();

        foreach (var line in lines)
        {
            foreach (var word in line.Words)
            {
                var coordinateV2 = ConvertBoundingBox2(word.BoundingBox, cropCoordinate);

                output.Add(new OcrResultV2Dto
                {
                    id = Guid.NewGuid().ToString(),
                    type = "number",
                    x = coordinateV2.X,
                    y = coordinateV2.Y,
                    width = coordinateV2.Width,
                    height = coordinateV2.Height,
                    text = word.Text,
                    probability = word.Confidence
                });
            }
        }

        return output;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="boundingBox"></param>
    /// <param name="cropCoordinate"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static CoordinateV2Dto ConvertBoundingBox2(IList<double?> boundingBox, CropCoordinate? cropCoordinate)
    {
        if (boundingBox is not { Count: 8 })
        {
            throw new ArgumentException("BoundingBox must contain 8 coordinates.");
        }

        var minX = double.MaxValue;
        var minY = double.MaxValue;
        var maxX = double.MinValue;
        var maxY = double.MinValue;

        for (var i = 0; i < boundingBox.Count; i += 2)
        {
            var x = boundingBox[i] ?? 0.0;
            var y = boundingBox[i + 1] ?? 0.0;

            if (x < minX) minX = x;
            if (y < minY) minY = y;
            if (x > maxX) maxX = x;
            if (y > maxY) maxY = y;
        }

        return new CoordinateV2Dto
        {
            X = minX + (cropCoordinate?.X ?? 0),
            Y = minY + (cropCoordinate?.Y ?? 0),
            Width = maxX - minX,
            Height = maxY - minY
        };
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ocrResults"></param>
    /// <param name="rows"></param>
    /// <returns></returns>
    public static List<OcrResultV2Dto> AssignRowIndex(List<OcrResultV2Dto> ocrResults, CropCoordinate[] rows)
    {
        foreach (var result in ocrResults)
        {
            result.rowIndex = rows
                .Select((row, index) => new { Row = row, Index = index })
                .FirstOrDefault(r =>
                    result.y >= r.Row.Y &&
                    result.y < (r.Row.Y + r.Row.Height)
                )?.Index;
        }

        return ocrResults;
    }
}