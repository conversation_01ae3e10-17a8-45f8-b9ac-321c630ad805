﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using aibase.Configuration;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.Models.Dto;
using aibase.StepWorkflowEntity;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using SkiaSharp;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace aibase.Models.Services.SegmentService;

/// <inheritdoc />
public class SegmentService : ISegmentService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ExternalServiceOptions _externalServiceOptions;

    /// <summary>
    ///
    /// </summary>
    /// <param name="httpClientFactory"></param>
    /// <param name="externalServiceOptions"></param>
    public SegmentService(IHttpClientFactory httpClientFactory,
        IOptions<ExternalServiceOptions> externalServiceOptions)
    {
        _httpClientFactory = httpClientFactory;
        _externalServiceOptions = externalServiceOptions.Value;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="Image"></param>
    /// <param name="url"></param>
    /// <param name="boundingRow"></param>
    /// <param name="boundingRowOption"></param>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    /// <exception cref="Exception"></exception>
    public async Task<SegmentResult> PerformSegment(IFormFile? Image, string? url,
        CropCoordinate[] boundingRow, BoundingRowOption boundingRowOption)
    {
        try
        {
            ServicePointManager.ServerCertificateValidationCallback =
                delegate { return true; };
            var result = string.Empty;
            var imageWidth = 0;
            var imageHeight = 0;
            var encodedImage = string.Empty;
            double[][] boundingBoxes;

            if (boundingRowOption == BoundingRowOption.AutoGenerate)
            {
                if (url == null && Image != null)
                {
                    await using (var imageStream = Image.OpenReadStream())
                    {
                        var rawImage = SKBitmap.Decode(imageStream);
                        if (rawImage == null)
                        {
                            throw new InvalidOperationException("Failed to decode image.");
                        }

                        imageWidth = rawImage.Width;
                        imageHeight = rawImage.Height;
                    }

                    var client = _httpClientFactory.CreateClient();
                    client.DefaultRequestHeaders.Add("Prediction-Key", _externalServiceOptions.Segment.PredictionKey);

                    using var content = new StreamContent(Image.OpenReadStream());
                    content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");

                    var response = await client.PostAsync(_externalServiceOptions.Segment.CustomVisionImageEndpoint, content);

                    result = await response.Content.ReadAsStringAsync();
                }

                if (url != null && Image == null)
                {
                    var client = _httpClientFactory.CreateClient();
                    client.DefaultRequestHeaders.Add("Prediction-Key", _externalServiceOptions.Segment.PredictionKey);

                    var jsonContent = new StringContent($"{{ \"Url\": \"{url}\" }}", Encoding.UTF8,
                        "application/json");

                    var response = await client.PostAsync(_externalServiceOptions.Segment.CustomVisionUrlEndpoint, jsonContent);

                    result = await response.Content.ReadAsStringAsync();
                    var (width, height) = await GetImageDimensionsFromUrl(url);
                    imageWidth = width;
                    imageHeight = height;
                }

                var resultDataTest = JsonDocument.Parse(result).RootElement;

                boundingBoxes = resultDataTest.GetProperty("predictions")
                    .EnumerateArray()
                    .Where(p => p.GetProperty("probability").GetDouble() > 0.95 &&
                                p.GetProperty("tagName").GetString() == ImageConstSettings.ImageCropRowLower)
                    .Select(p =>
                    {
                        var bbox = p.GetProperty("boundingBox");
                        var x = bbox.GetProperty("left").GetDouble() * imageWidth;
                        var y = bbox.GetProperty("top").GetDouble() * imageHeight;
                        var w = bbox.GetProperty("width").GetDouble() * imageWidth;
                        var h = bbox.GetProperty("height").GetDouble() * imageHeight;
                        return new[] { x, y, w, h };
                    })
                    .ToArray();
            }
            else
            {
                boundingBoxes = boundingRow.Select(cc => new[]
                {
                    cc.X,
                    cc.Y,
                    cc.Width,
                    cc.Height
                }).ToArray();
            }

            if (url == null && Image != null)
                encodedImage = await EncodeImageToBase64(Image);
            if (url != null && Image == null)
                encodedImage = await EncodeImageToBase64FromUrl(url);

            try
            {
                // Convert the mask to a binary image using OpenCvSharp
                var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromMinutes(5);
                var jsonContent = new StringContent($@"{{
                        ""image"": ""{encodedImage}"",
                        ""bboxes"": {JsonSerializer.Serialize(boundingBoxes)}
                    }}", Encoding.UTF8, "application/json");

                var response = await client.PostAsync(_externalServiceOptions.Segment.SegmentGpuEndpoint, jsonContent);
                var imagePathResponse = await response.Content.ReadAsStringAsync();
                var jsonResponse = JObject.Parse(imagePathResponse);

                // Extract the combined_image base64 string
                var base64Image = jsonResponse["combined_image"]?.ToString();
                var maskContourPoints = jsonResponse["mask_contour_points"];
                var segmentIndices = jsonResponse["segment_indices"];

                if (string.IsNullOrEmpty(base64Image))
                {
                    throw new InvalidOperationException("No image found in the response");
                }

                var resultData = new SegmentResult
                {
                    MaskContourPoints = ConvertJTokenToNestedList(maskContourPoints),
                    SegmentIndices = segmentIndices?.ToObject<int[]>() ?? [],
                };
                return resultData;
            }
            catch (WebException ex)
            {
                if (ex.Response != null)
                {
                    using var response = ex.Response;
                    Console.WriteLine("The request failed with status code: " +
                                      ((HttpWebResponse)response).StatusCode);
                    await using var responseStream = response.GetResponseStream();
                    using var reader = new StreamReader(responseStream);
                    var errorResult = await reader.ReadToEndAsync();
                    throw new Exception(errorResult);
                    //return new ContentResult { Content = errorResult, ContentType = "application/json", StatusCode = (int)((HttpWebResponse)response).StatusCode };
                }
                else
                {
                    Console.WriteLine(ex.Message);
                    //return new ContentResult { Content = ex.Message, ContentType = "text/plain", StatusCode = 500 };
                    throw new Exception(ex.Message);
                }
            }
        }
        catch (WebException ex)
        {
            if (ex.Response != null)
            {
                using var response = ex.Response;
                Console.WriteLine("The request failed with status code: " + ((HttpWebResponse)response).StatusCode);
                await using var responseStream = response.GetResponseStream();
                using var reader = new StreamReader(responseStream);
                var errorResult = await reader.ReadToEndAsync();
                throw new Exception(errorResult);
                //return new ContentResult { Content = errorResult, ContentType = "application/json", StatusCode = (int)((HttpWebResponse)response).StatusCode };
            }
            else
            {
                Console.WriteLine(ex.Message);
                throw new Exception(ex.Message);
                //return new ContentResult { Content = ex.Message, ContentType = "text/plain", StatusCode = 500 };
            }
        }
    }

    private async Task<(int width, int height)> GetImageDimensionsFromUrl(string imageUrl)
    {
        var httpClient = _httpClientFactory.CreateClient();

        // Fetch the image data from the URL
        var imageData = await httpClient.GetByteArrayAsync(imageUrl);

        // Decode the image to get its dimensions
        using var imageStream = new MemoryStream(imageData);
        var bitmap = SKBitmap.Decode(imageStream);
        if (bitmap == null)
        {
            throw new InvalidOperationException("Failed to decode the image from the URL.");
        }

        // Return the width and height of the image
        return (bitmap.Width, bitmap.Height);
    }

    private static async Task<string> EncodeImageToBase64(IFormFile image)
    {
        using var memoryStream = new MemoryStream();
        await image.CopyToAsync(memoryStream);
        var imageBytes = memoryStream.ToArray();
        return Convert.ToBase64String(imageBytes);
    }

    private async Task<string> EncodeImageToBase64FromUrl(string imageUrl)
    {
        var httpClient = _httpClientFactory.CreateClient();
        var imageData = await httpClient.GetByteArrayAsync(imageUrl);
        var base64Image = Convert.ToBase64String(imageData);
        return base64Image;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static List<List<List<double>>> ConvertJTokenToNestedList(JToken? token)
    {
        // Ensure the token is not null
        if (token == null)
        {
            return [];
        }

        // Parse the JToken to a List<List<List<double>>>
        var list = new List<List<List<double>>>();

        foreach (var innerArray1 in token)
        {
            var innerList1 = new List<List<double>>();
            foreach (var innerArray2 in innerArray1)
            {
                var innerList2 = new List<double>();
                foreach (var value in innerArray2)
                {
                    innerList2.Add((double)value);
                }

                innerList1.Add(innerList2);
            }

            list.Add(innerList1);
        }

        return list;
    }
}