using System.ComponentModel.DataAnnotations;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
public class CloneNodeInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int NodeIdToClone { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? NewParentNodeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? TargetRockTreeRootId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [StringLength(255)]
    public string? NewName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IncludeDescendants { get; set; } = true;
}