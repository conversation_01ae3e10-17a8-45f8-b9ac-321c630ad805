using System.ComponentModel.DataAnnotations;
using Abp.AutoMapper;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
[AutoMapTo(typeof(RockTreeRoot))]
public class CreateRockTreeRootInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    [StringLength(255)] // Assuming a max length for Name
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(1000)] // Assuming a max length for Description
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? GeologySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; } = true;
}