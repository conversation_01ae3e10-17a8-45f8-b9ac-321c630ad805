using System.Threading.Tasks;

namespace AiBase.Services;

/// <summary>
/// Interface for email service
/// </summary>
public interface IMailer
{
    /// <summary>
    /// Send verification email to user
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <param name="firstName">User's first name</param>
    /// <param name="lastName">User's last name</param>
    /// <param name="userId">User's ID</param>
    /// <param name="token">Verification token</param>
    Task SendVerificationEmailAsync(string email, string firstName, string lastName, long userId, string token);

    /// <summary>
    /// Send password reset email to user
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <param name="firstName">User's first name</param>
    /// <param name="lastName">User's last name</param>
    /// <param name="resetToken">Password reset token</param>
    Task SendPasswordResetEmailAsync(string email, string firstName, string lastName, string resetToken);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="email"></param>
    /// <param name="firstName"></param>
    /// <param name="lastName"></param>
    /// <param name="resetToken"></param>
    /// <returns></returns>
    Task SendPasswordAdminCreateAccountAsync(string email, string firstName, string lastName, string resetToken);

    /// <summary>
    /// Send email with custom subject and body
    /// </summary>
    /// <param name="email">Recipient email address</param>
    /// <param name="subject">Email subject</param>
    /// <param name="body">Email body (HTML)</param>
    Task SendEmailAsync(string email, string subject, string body);
}