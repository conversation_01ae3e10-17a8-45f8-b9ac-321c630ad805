﻿using System;
using System.Collections.Generic;
using aibase.SourceTypeWorkflowEntity;
using aibase.StepWorkflowEntity;
using System.ComponentModel.DataAnnotations;

namespace aibase.StepWorkflows.Dto;

/// <summary>
/// 
/// </summary>
public class CreateStepDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int WorkflowId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public ProcessType ProcessType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public AiModelType? ModelId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ToolType? ToolType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? PolygonId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public DataSourceType DataSourceType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public Boolean SegmentFlag { get; set; } = false;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string DataValue { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public OutputType OutputType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingBoxId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingRowsId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public BoundingRowOption? BoundingRowOption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Prompt { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsCropAdditional { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageTypesAdditional { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageSubtypesAdditional { get; set; } = [];
}