using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.DrillHoleEntity;

namespace aibase.DesurveyResults;

public class DesurveyResult : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public int DrillHoleId { get; set; }
    public virtual DrillHole DrillHole { get; set; }
    
    [Required]
    public double Depth { get; set; }
    
    [Required]
    public double Northing { get; set; }
    
    [Required]
    public double Easting { get; set; }
    
    [Required]
    public double Elevation { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}