using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.DrillHoleEntity;
using aibase.RqdCalculations;

namespace aibase.RqdPercentResults;

public class RqdPercentResult : Entity, IHasCreationTime, IHasModificationTime
{
    [Required]
    public double OcrValueFrom { get; set; }
    
    [Required]
    public double FromX { get; set; }
    
    [Required]
    public double FromY { get; set; }
    
    [Required]
    public int FromImageCropId { get; set; }
    
    [Required]
    public int FromRowIndex { get; set; }
    
    [Required]
    public string FromOcrId { get; set; } = string.Empty;
    
    [Required]
    public double OcrValueTo { get; set; }
    
    [Required]
    public double ToX { get; set; }
    
    [Required]
    public double ToY { get; set; }
    
    [Required]
    public int ToImageCropId { get; set; }
    
    [Required]
    public int ToRowIndex { get; set; }
    
    [Required]
    public string ToOcrId { get; set; } = string.Empty;
    
    [Required]
    public double Length { get; set; }
    
    [Required]
    public double Total { get; set; }
    
    [Required]
    public int NumberOfPieces { get; set; }
    
    [Required]
    public double DepthInterval { get; set; }
    
    [Required]
    public int DrillHoleId { get; set; }
    public virtual DrillHole DrillHole { get; set; }
    
    [Required]
    public int RqdCalculationId { get; set; }
    public virtual RqdCalculation RqdCalculation { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}