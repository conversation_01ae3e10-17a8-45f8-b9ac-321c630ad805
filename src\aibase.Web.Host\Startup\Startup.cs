using System;
using System.Linq;
using System.Reflection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Castle.Facilities.Logging;
using Abp.AspNetCore;
using Abp.AspNetCore.Mvc.Antiforgery;
using Abp.Castle.Logging.Log4Net;
using Abp.Extensions;
using aibase.Configuration;
using aibase.Identity;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using System.IO;
using aibase.APIKeys.Services.ApiKeyAuthentication;
using aibase.IsolationTenant;
using Hangfire;
using Hangfire.PostgreSql;
using Hangfire.Dashboard;
using Hangfire.Dashboard.BasicAuthorization;
using aibase.JobEntity;
using aibase.Jobs.Services.Socket;
using aibase.Logging;
using aibase.ProjectEntity;
using aibase.Prospects;
using aibase.Suites;
using aibase.Web.Host.CloudLogging;
using aibase.SentryMiddleware;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.Http.Features;

namespace aibase.Web.Host.Startup
{
    /// <summary>
    /// 
    /// </summary>
    public class Startup
    {
        private const string DefaultCorsPolicyName = "localhost";

        private const string ApiVersion = "v1";

        private readonly IConfigurationRoot _appConfiguration;
        private readonly IWebHostEnvironment _hostingEnvironment;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="env"></param>
        public Startup(IWebHostEnvironment env)
        {
            _hostingEnvironment = env;
            _appConfiguration = env.GetAppConfiguration();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            var builder = WebApplication.CreateBuilder();
            
            // Add LoggingService
            services.AddSingleton<ILoggingService,LoggingService>();
            //Add GenericEntityServices
            services.AddTransient<IGenericEntityService<Project>, GenericEntityService<Project>>();
            services.AddTransient<IGenericEntityService<Prospect>, GenericEntityService<Prospect>>();
            services.AddTransient<IGenericEntityService<Suite>, GenericEntityService<Suite>>();
            services.AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = "ApiKey";
                    options.DefaultChallengeScheme = "ApiKey";
                })
                .AddScheme<AuthenticationSchemeOptions, ApiKeyAuthenticationHandler>("ApiKey", options => { });

            // Configure file size limits
            services.Configure<IISServerOptions>(options =>
            {
                options.MaxRequestBodySize = 104857600; // 100MB in bytes
            });

            services.Configure<KestrelServerOptions>(options =>
            {
                options.Limits.MaxRequestBodySize = 104857600; // 100MB in bytes
            });

            services.Configure<FormOptions>(options =>
            {
                options.MultipartBodyLengthLimit = 104857600; // 100MB in bytes
            });

            //MVC
            services.AddControllersWithViews(options =>
            {
                options.Filters.Add(new AbpAutoValidateAntiforgeryTokenAttribute());
            });

            IdentityRegistrar.Register(services);
            AuthConfigurer.Configure(services, _appConfiguration);
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

            services.AddSignalR();

            // Add HttpClient for Teams notifications
            services.AddHttpClient();

            // Configure external service options
            services.Configure<ExternalServiceOptions>(_appConfiguration.GetSection(ExternalServiceOptions.SectionName));

            // Configure CORS for angular2 UI
            services.AddCors(options =>
            {
                options.AddPolicy(DefaultCorsPolicyName, build =>
                {
                    var allowAllCors = _appConfiguration.GetValue<string>("env");

                    if (allowAllCors == "dev")
                    {
                        build.AllowAnyOrigin()
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                    }
                    else
                    {
                        build.WithOrigins(
                                // App:CorsOrigins in appSettings.json can contain more than one address separated by comma.
                                _appConfiguration["App:CorsOrigins"]!
                                    .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                    .Select(o => o.RemovePostFix("/"))
                                    .ToArray()
                            )
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .AllowCredentials();
                    }
                });
            });


            // Swagger - Enable this line and the related lines in Configure method to enable swagger UI
            ConfigureSwagger(services);

            // Configure Abp and Dependency Injection
            string log4NetConfigFile = _hostingEnvironment.IsDevelopment()
                ? "log4net.config"
                : _hostingEnvironment.IsStaging()
                    ? "log4net.Staging.config"
                    : "log4net.Production.config";
            services.AddAbpWithoutCreatingServiceProvider<aibaseWebHostModule>(
                // Configure Log4Net logging
                options => options.IocManager.IocContainer.AddFacility<LoggingFacility>(
                    f => f.UseAbpLog4Net().WithConfig(log4NetConfigFile)
                )
            );
            // Hangfire to run background jobs
            services.AddHangfire(x =>
                x.UsePostgreSqlStorage(_appConfiguration["ConnectionStrings:Default"], new PostgreSqlStorageOptions
                    {
                        SchemaName = "hangfire"
                    }
                ));

            // services.AddTransient<ApiKeyAuthorizeFilter>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="loggerFactory"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            app.UseAbp(options => { options.UseAbpRequestLocalization = false; }); // Initializes ABP framework.

            app.UseCors(DefaultCorsPolicyName); // Enable CORS!

            app.UseStaticFiles();
            
            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();
            
            app.UseAbpRequestLocalization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHub<SocketWorkflowJob>("/signalr-WorkflowJob");
                endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
                endpoints.MapControllerRoute("defaultWithArea", "{area}/{controller=Home}/{action=Index}/{id?}");
            });

            // Enable middleware to serve generated Swagger as a JSON endpoint
            app.UseSwagger(c => { c.RouteTemplate = "swagger/{documentName}/swagger.json"; });

            // Enable middleware to serve swagger-ui assets (HTML, JS, CSS etc.)
            app.UseSwaggerUI(options =>
            {
                // specifying the Swagger JSON endpoint.
                options.SwaggerEndpoint($"/swagger/{ApiVersion}/swagger.json", $"aibase API {ApiVersion}");
                options.IndexStream = () => Assembly.GetExecutingAssembly()
                    .GetManifestResourceStream("aibase.Web.Host.wwwroot.swagger.ui.index.html");
                options.DisplayRequestDuration(); // Controls the display of the request duration (in milliseconds) for "Try it out" requests.
            }); // URL: /swagger

            // Hangfire UI to manage
            app.UseHangfireServer(new BackgroundJobServerOptions
            {
                Queues = ["default", JobConstants.WorkflowQueue]
            });
            app.UseHangfireDashboard("/hangfire", new DashboardOptions
            {
                Authorization =
                [
                    new BasicAuthAuthorizationFilter(new BasicAuthAuthorizationFilterOptions
                    {
                        RequireSsl = false,
                        SslRedirect = false,
                        LoginCaseSensitive = false,
                        Users =
                        [
                            new BasicAuthAuthorizationUser
                            {
                                Login = _appConfiguration["Hangfire:username"],
                                PasswordClear = _appConfiguration["Hangfire:password"],
                            }
                        ],
                    })
                ],
                IsReadOnlyFunc = (DashboardContext context) => true,
            });
            //Enable send User Infor to Sentry
            app.UseMiddleware<SentryUserScopeMiddleware>();
        }

        private void ConfigureSwagger(IServiceCollection services)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc(ApiVersion, new OpenApiInfo
                {
                    Version = ApiVersion,
                    Title = $"aibase API {environmentName}",
                    Description = "aibase",
                    // uncomment if needed TermsOfService = new Uri("https://example.com/terms"),
                    Contact = new OpenApiContact
                    {
                        Name = "aibase",
                        Email = string.Empty,
                        Url = new Uri("https://twitter.com/aspboilerplate"),
                    },
                    License = new OpenApiLicense
                    {
                        Name = "MIT License",
                        Url = new Uri("https://github.com/aspnetboilerplate/aspnetboilerplate/blob/dev/LICENSE"),
                    }
                });
                options.DocInclusionPredicate((docName, description) => true);

                // Define the BearerAuth scheme that's in use
                options.AddSecurityDefinition("bearerAuth", new OpenApiSecurityScheme()
                {
                    Description =
                        "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey
                });

                //add summaries to swagger
                var canShowSummaries = _appConfiguration.GetValue<bool>("Swagger:ShowSummaries");
                if (canShowSummaries)
                {
                    var hostXmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var hostXmlPath = Path.Combine(AppContext.BaseDirectory, hostXmlFile);
                    options.IncludeXmlComments(hostXmlPath);

                    const string applicationXml = $"aibase.Application.xml";
                    var applicationXmlPath = Path.Combine(AppContext.BaseDirectory, applicationXml);
                    options.IncludeXmlComments(applicationXmlPath);

                    const string webCoreXmlFile = $"aibase.Web.Core.xml";
                    var webCoreXmlPath = Path.Combine(AppContext.BaseDirectory, webCoreXmlFile);
                    options.IncludeXmlComments(webCoreXmlPath);
                }
            });
        }
    }
}