using System.Collections.Generic;

namespace aibase.RockTree.Dto;

/// <summary>
/// Represents a hierarchical tree node structure
/// </summary>
public class TreeNodeDto
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public RockNodeType NodeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? DisplayColor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? IconUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ParentId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<TreeNodeDto> Children { get; set; } = [];
}

/// <summary>
/// Represents the complete tree structure for a tenant
/// </summary>
public class TreeStructureDto
{
    /// <summary>
    /// 
    /// </summary>
    public int TenantId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<TreeNodeDto> RootNodes { get; set; } = [];
}