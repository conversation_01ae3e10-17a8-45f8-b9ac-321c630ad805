using System.ComponentModel.DataAnnotations;
using Abp.AutoMapper;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
[AutoMapTo(typeof(RockNode))]
public class CreateRockNodeInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(50)]
    public string? Code { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public RockNodeType NodeType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ParentNodeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTreeRootId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    [StringLength(7)]
    public string? DisplayColor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [StringLength(2048)]
    public string? IconUrl { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? Order { get; set; }
}