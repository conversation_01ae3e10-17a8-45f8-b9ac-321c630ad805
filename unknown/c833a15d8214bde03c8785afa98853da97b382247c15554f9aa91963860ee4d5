using System.ComponentModel.DataAnnotations;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
public class MoveNodeInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int NodeIdToMove { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? NewParentNodeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? NewOrder { get; set; }
}