using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.RockTree.Dto;

/// <summary>
/// 
/// </summary>
public class ReorderSiblingsInput
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ParentNodeId { get; set; } 

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public List<int> OrderedNodeIds { get; set; } = [];
}